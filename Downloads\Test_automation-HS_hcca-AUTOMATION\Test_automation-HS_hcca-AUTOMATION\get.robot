*** Settings ***
Library    Process
Library    OperatingSystem
Library    DateTime
Library    Collections
Library    BuiltIn
Library    pyautogui

*** Variables ***
${HS_DIR}                 C:\\Users\\<USER>\\Desktop\\SX-Houston-server_v142\\SX-Houston-server_v142
${HCCA_DIR}               C:\\Users\\<USER>\\Desktop\\SX-Houston-app_v212
${HS_PATH}                ${HS_DIR}\\houston_server.exe
${HCCA_PATH}              ${HCCA_DIR}\\houston_app.exe
${SEARCH_BOX_IMAGE}       ${CURDIR}\\search_box.png
${SEND_BUTTON_IMAGE}      ${CURDIR}\\send_button.png
${CONFIDENCE_LEVEL}       0.7

*** Keywords ***
Setup Environment
    [Documentation]    Starts both Houston Server and HCCA
    Kill Process If Running    houston_server.exe
    Kill Process If Running    houston_app.exe
    BuiltIn.Sleep    2s
    
    # Start Houston Server
    ${hs_handle}=    Start Process    ${HS_PATH}    shell=True    cwd=${HS_DIR}
    BuiltIn.Sleep    5s
    Log    ✅ Houston Server started    console=True
    
    # Start HCCA
    ${hcca_handle}=    Start Process    ${HCCA_PATH}    shell=True    cwd=${HCCA_DIR}
    BuiltIn.Sleep    5s
    Log    ✅ HCCA started    console=True
    
    RETURN    ${hs_handle}    ${hcca_handle}

Kill Process If Running
    [Documentation]    Kills a process if it's running
    [Arguments]    ${process_name}
    Run Process    taskkill    /F    /IM    ${process_name}    /T    shell=True
    BuiltIn.Sleep    1s

Find And Click Image
    [Documentation]    Finds and clicks an image on screen
    [Arguments]    ${image_path}    ${confidence}=${CONFIDENCE_LEVEL}    ${double_click}=False
    Log    Looking for image: ${image_path}    console=True
    
    # Try to locate the image
    ${location}=    Run Keyword And Return Status    
    ...    pyautogui.locateOnScreen    ${image_path}    confidence=${confidence}
    
    # If image found, click it
    Run Keyword If    ${location}    
    ...    Run Keyword If    ${double_click}    pyautogui.doubleClick    ${location}
    ...    ELSE    pyautogui.click    ${location}
    
    # Return success/failure
    RETURN    ${location}

Send Command
    [Documentation]    Sends a command using PyAutoGUI
    [Arguments]    ${command_name}
    
    Log    Attempting to send command: ${command_name}    console=True
    
    # Try to find and click the search box
    ${search_box_found}=    Find And Click Image    ${SEARCH_BOX_IMAGE}
    
    # If search box found, type the command
    Run Keyword If    ${search_box_found}    
    ...    pyautogui.typewrite    ${command_name}
    ...    ELSE    Log    ⚠️ Search box not found    console=True
    
    BuiltIn.Sleep    1s
    
    # Try to find and double-click the command
    ${command_image}=    Set Variable    ${CURDIR}\\${command_name}.png
    ${command_found}=    Find And Click Image    ${command_image}    double_click=True
    
    Run Keyword If    not ${command_found}    
    ...    Log    ⚠️ Command not found: ${command_name}    console=True
    
    BuiltIn.Sleep    1s
    
    # Try to find and click the send button
    ${send_button_found}=    Find And Click Image    ${SEND_BUTTON_IMAGE}
    
    Run Keyword If    not ${send_button_found}    
    ...    Log    ⚠️ Send button not found    console=True
    
    # Return overall success/failure
    ${success}=    Evaluate    ${search_box_found} and ${command_found} and ${send_button_found}
    RETURN    ${success}

Search And Double Click Command
    [Documentation]    Searches for a command in the search field and double-clicks it
    [Arguments]    ${command_name}
    
    Log    Searching for command: ${command_name}    console=True
    
    # First, click on the search field
    ${search_field_image}=    Set Variable    ${CURDIR}\\search_field.png
    ${search_field_found}=    Run Keyword And Return Status    
    ...    pyautogui.locateOnScreen    ${search_field_image}    confidence=${CONFIDENCE_LEVEL}
    
    Run Keyword If    ${search_field_found}    
    ...    pyautogui.click    pyautogui.locateOnScreen(${search_field_image}, confidence=${CONFIDENCE_LEVEL})
    ...    ELSE    Log    ⚠️ Search field not found    console=True
    
    # If search field found, type the command
    Run Keyword If    ${search_field_found}    
    ...    pyautogui.typewrite    ${command_name}
    
    BuiltIn.Sleep    1s
    
    # Look for the command in the results and double-click it
    ${command_found}=    Run Keyword And Return Status
    ...    pyautogui.locateOnScreen    ${CURDIR}\\${command_name}.png    confidence=${CONFIDENCE_LEVEL}
    
    Run Keyword If    ${command_found}    
    ...    pyautogui.doubleClick    pyautogui.locateOnScreen(${CURDIR}\\${command_name}.png, confidence=${CONFIDENCE_LEVEL})
    ...    ELSE    Log    ⚠️ Command not found in results: ${command_name}    console=True
    
    BuiltIn.Sleep    1s
    
    # Return success/failure
    RETURN    ${command_found}

*** Test Cases ***
Send All Commands
    [Documentation]    Sends all commands in the list
    
    # Setup environment
    ${hs_handle}    ${hcca_handle}=    Setup Environment
    
    # Define commands to send
    @{commands}=    Create List    get_app_period    set_time    reset_config    get_version
    
    # Try to send each command
    FOR    ${cmd}    IN    @{commands}
        ${cmd_success}=    Send Command    ${cmd}
        Run Keyword If    not ${cmd_success}    
        ...    Log    ⚠️ Failed to send command: ${cmd}    console=True
        ...    ELSE    Log    ✅ Successfully sent command: ${cmd}    console=True
        BuiltIn.Sleep    2s
    END
    
    # Cleanup
    Log    Cleaning up - closing applications...    console=True
    Kill Process If Running    houston_server.exe
    Kill Process If Running    houston_app.exe
    
    # Log success
    Log    ✅ Test completed    console=True

Search For Get App Period
    [Documentation]    Searches for get_app_period command and double-clicks it
    
    # Setup environment
    ${hs_handle}    ${hcca_handle}=    Setup Environment
    
    # Take a screenshot of the search field
    Log    Please take a screenshot of the empty search field and save it as "search_field.png"    console=True
    Log    Press Enter when done...    console=True
    Run Process    cmd    /c    pause    shell=True
    
    # Take a screenshot of the get_app_period command in the results
    Log    Please search for "get_app_period", then take a screenshot of it in the results and save as "get_app_period.png"    console=True
    Log    Press Enter when done...    console=True
    Run Process    cmd    /c    pause    shell=True
    
    # Now run the search and double-click
    ${success}=    Search And Double Click Command    get_app_period
    
    Run Keyword If    ${success}    
    ...    Log    ✅ Successfully found and double-clicked get_app_period    console=True
    ...    ELSE    Log    ❌ Failed to find and double-click get_app_period    console=True
    
    # Cleanup
    BuiltIn.Sleep    5s
    Log    Cleaning up - closing applications...    console=True
    Kill Process If Running    houston_server.exe
    Kill Process If Running    houston_app.exe
    
    # Log success
    Log    ✅ Test completed    console=True
