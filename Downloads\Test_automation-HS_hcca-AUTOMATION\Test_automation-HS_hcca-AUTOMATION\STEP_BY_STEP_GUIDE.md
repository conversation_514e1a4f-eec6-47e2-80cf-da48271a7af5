# Houston Test Automation - Step-by-Step Guide

## Overview
This guide will help you successfully run the Houston Server and HCCA test automation scenario.

## Current Status
✅ Robot Framework is installed (version 7.3)
✅ Houston Server executable exists
✅ Houston HCCA executable exists
❌ Missing image files for UI automation

## Step-by-Step Instructions

### Step 1: Start the Applications Manually
Before running the automated tests, let's start the applications manually to capture the required images.

1. **Start Houston Server:**
   ```
   Navigate to: C:\Users\<USER>\Desktop\SX-Houston-server_v142\SX-Houston-server_v142\
   Double-click: houston_server.exe
   ```

2. **Start Houston Control Center Application (HCCA):**
   ```
   Navigate to: C:\Users\<USER>\Desktop\SX-Houston-app_v212\
   Double-click: houston_app.exe
   ```

3. **Wait for both applications to fully load** (approximately 10-15 seconds)

### Step 2: Capture Required Images
The test automation uses image recognition to interact with the GUI. We need to capture 4 images:

1. **Run the image capture helper:**
   ```bash
   python capture_images.py
   ```

2. **Follow the prompts to capture each image:**
   - `search_field.png` - The empty search box where you type commands
   - `set_time.png` - The "set_time" command in search results
   - `reset_config.png` - The "reset_config" command in search results  
   - `get_version.png` - The "get_version" command in search results

3. **For each image capture:**
   - Position the HCCA window so the element is visible
   - Follow the script prompts to define the capture area
   - Use precise mouse positioning for best results

### Step 3: Verify Image Files
After capturing, verify all required files exist:

```bash
dir *.png
```

You should see:
- ✅ get_app_period.png (already exists)
- ✅ search_box.png (already exists)
- ✅ send_button.png (already exists)
- ✅ search_field.png (newly captured)
- ✅ set_time.png (newly captured)
- ✅ reset_config.png (newly captured)
- ✅ get_version.png (newly captured)

### Step 4: Run the Test
Once all images are captured, run the Robot Framework test:

```bash
robot get.robot
```

### Step 5: Monitor Test Execution
The test will:
1. ✅ Start Houston Server
2. ✅ Start HCCA
3. 🔄 Attempt to send each command:
   - get_app_period
   - set_time
   - reset_config
   - get_version
4. ✅ Clean up (close applications)

### Step 6: Review Results
After the test completes:
1. Check the console output for success/failure messages
2. Review the generated reports:
   - `log.html` - Detailed execution log
   - `report.html` - Test summary report
   - `output.xml` - Raw test data

## Troubleshooting

### Common Issues:

1. **"Image not found" errors:**
   - Recapture the image with better precision
   - Adjust confidence level (currently 0.7)
   - Ensure the UI element is fully visible

2. **Applications don't start:**
   - Check if processes are already running
   - Verify file paths in the robot file
   - Run applications manually first

3. **Permission errors:**
   - Run command prompt as Administrator
   - Check file permissions

### Alternative Test Scenarios:

1. **Run individual test case:**
   ```bash
   robot -t "Send All Commands" get.robot
   ```

2. **Run with different confidence level:**
   Edit get.robot and change `${CONFIDENCE_LEVEL}` from 0.7 to 0.8 or 0.6

3. **Debug mode with more logging:**
   ```bash
   robot -L DEBUG get.robot
   ```

## Next Steps
After successful execution:
1. Review the test results
2. Optimize image recognition if needed
3. Add more test scenarios
4. Integrate into CI/CD pipeline

## Support
If you encounter issues:
1. Check the generated log.html for detailed error information
2. Verify all image files are properly captured
3. Ensure both applications are compatible with automation
