#!/usr/bin/env python3
"""
Keyboard-based automation for Houston HCCA Desktop Application
This script uses keyboard shortcuts and typing instead of image recognition
"""

import pyautogui
import time
import sys

# Configure pyautogui
pyautogui.FAILSAFE = True  # Move mouse to top-left corner to stop
pyautogui.PAUSE = 0.5      # Pause between actions

def log_action(message):
    """Log actions with timestamp"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def wait_and_log(seconds, message):
    """Wait with logging"""
    log_action(f"Waiting {seconds}s - {message}")
    time.sleep(seconds)

def send_command_method1(command_name):
    """
    Method 1: Click search bar, type command, press Enter, double-click result, click Send
    """
    log_action(f"=== METHOD 1: Sending command '{command_name}' ===")
    
    try:
        # Step 1: Click on search bar (assuming it's focused or we can tab to it)
        log_action("Step 1: Focusing on search bar")
        pyautogui.click(500, 300)  # Adjust coordinates as needed
        wait_and_log(1, "Search bar focused")
        
        # Alternative: Use Tab to navigate to search field
        # pyautogui.press('tab')
        
        # Step 2: Clear any existing text and type the command
        log_action(f"Step 2: Typing command '{command_name}'")
        pyautogui.hotkey('ctrl', 'a')  # Select all existing text
        pyautogui.typewrite(command_name)
        wait_and_log(1, "Command typed")
        
        # Step 3: Press Enter to search
        log_action("Step 3: Pressing Enter to search")
        pyautogui.press('enter')
        wait_and_log(2, "Search executed, waiting for results")
        
        # Step 4: Double-click on the command result
        log_action("Step 4: Double-clicking on command result")
        # Assuming the first result is selected or we need to navigate to it
        pyautogui.press('down')  # Navigate to first result if needed
        pyautogui.press('enter')  # Or double-click
        pyautogui.press('enter')  # Double-click simulation
        wait_and_log(1, "Command selected")
        
        # Step 5: Click Send button (or use keyboard shortcut)
        log_action("Step 5: Clicking Send button")
        pyautogui.press('tab')  # Navigate to Send button
        pyautogui.press('enter')  # Click Send
        wait_and_log(2, "Send button clicked")
        
        log_action(f"✅ Successfully sent command '{command_name}' using Method 1")
        return True
        
    except Exception as e:
        log_action(f"❌ Error in Method 1: {e}")
        return False

def send_command_method2(command_name):
    """
    Method 2: Type command and use Ctrl+G shortcut
    """
    log_action(f"=== METHOD 2: Sending command '{command_name}' with Ctrl+G ===")
    
    try:
        # Step 1: Focus on search bar
        log_action("Step 1: Focusing on search bar")
        pyautogui.click(500, 300)  # Adjust coordinates as needed
        wait_and_log(1, "Search bar focused")
        
        # Step 2: Clear and type command
        log_action(f"Step 2: Typing command '{command_name}'")
        pyautogui.hotkey('ctrl', 'a')  # Select all
        pyautogui.typewrite(command_name)
        wait_and_log(1, "Command typed")
        
        # Step 3: Use Ctrl+G shortcut to execute
        log_action("Step 3: Using Ctrl+G shortcut")
        pyautogui.hotkey('ctrl', 'g')
        wait_and_log(2, "Ctrl+G executed")
        
        log_action(f"✅ Successfully sent command '{command_name}' using Method 2 (Ctrl+G)")
        return True
        
    except Exception as e:
        log_action(f"❌ Error in Method 2: {e}")
        return False

def send_command_method3(command_name):
    """
    Method 3: Advanced keyboard navigation
    """
    log_action(f"=== METHOD 3: Advanced keyboard navigation for '{command_name}' ===")
    
    try:
        # Step 1: Ensure we're in the right window
        log_action("Step 1: Ensuring HCCA window is active")
        pyautogui.hotkey('alt', 'tab')  # Switch to HCCA if needed
        wait_and_log(1, "Window focused")
        
        # Step 2: Navigate to search field using keyboard
        log_action("Step 2: Navigating to search field")
        pyautogui.hotkey('ctrl', 'f')  # Common shortcut for search
        # Alternative navigation methods:
        # pyautogui.press('f3')  # Another common search shortcut
        # pyautogui.hotkey('ctrl', 'l')  # Location/search bar
        wait_and_log(1, "Search field focused")
        
        # Step 3: Type command
        log_action(f"Step 3: Typing '{command_name}'")
        pyautogui.typewrite(command_name)
        wait_and_log(1, "Command typed")
        
        # Step 4: Navigate and select result
        log_action("Step 4: Selecting command from results")
        pyautogui.press('enter')  # Search
        wait_and_log(1, "Search executed")
        
        pyautogui.press('down')   # Navigate to first result
        pyautogui.press('enter')  # Select result
        wait_and_log(1, "Result selected")
        
        # Step 5: Execute command
        log_action("Step 5: Executing command")
        pyautogui.press('tab')    # Navigate to Send/Execute button
        pyautogui.press('enter')  # Execute
        wait_and_log(2, "Command executed")
        
        log_action(f"✅ Successfully sent command '{command_name}' using Method 3")
        return True
        
    except Exception as e:
        log_action(f"❌ Error in Method 3: {e}")
        return False

def test_all_commands():
    """Test all commands using different methods"""
    commands = ['get_app_period', 'set_time', 'reset_config', 'get_version']
    
    log_action("🚀 Starting automated command testing")
    log_action("Make sure HCCA application is open and visible")
    
    # Give user time to prepare
    for i in range(5, 0, -1):
        print(f"Starting in {i} seconds... (Move mouse to top-left corner to stop)")
        time.sleep(1)
    
    success_count = 0
    
    for i, command in enumerate(commands):
        log_action(f"\n{'='*60}")
        log_action(f"Testing command {i+1}/4: {command}")
        log_action(f"{'='*60}")
        
        # Try Method 2 first (Ctrl+G) as it's most reliable
        if send_command_method2(command):
            success_count += 1
        else:
            log_action(f"Method 2 failed, trying Method 1 for {command}")
            if send_command_method1(command):
                success_count += 1
            else:
                log_action(f"Both methods failed for {command}")
        
        # Wait between commands
        if i < len(commands) - 1:
            wait_and_log(3, "Waiting before next command")
    
    log_action(f"\n🎯 SUMMARY: {success_count}/{len(commands)} commands sent successfully")
    return success_count == len(commands)

def interactive_mode():
    """Interactive mode for testing individual commands"""
    log_action("🎮 Interactive Mode - Test individual commands")
    
    while True:
        print("\n" + "="*50)
        print("Choose an option:")
        print("1. Send 'get_app_period' (Method 1)")
        print("2. Send 'get_app_period' (Method 2 - Ctrl+G)")
        print("3. Send custom command (Method 2)")
        print("4. Test all commands automatically")
        print("5. Exit")
        
        choice = input("Enter your choice (1-5): ").strip()
        
        if choice == '1':
            send_command_method1('get_app_period')
        elif choice == '2':
            send_command_method2('get_app_period')
        elif choice == '3':
            command = input("Enter command name: ").strip()
            if command:
                send_command_method2(command)
        elif choice == '4':
            test_all_commands()
        elif choice == '5':
            log_action("👋 Exiting interactive mode")
            break
        else:
            print("Invalid choice. Please try again.")

def main():
    """Main function"""
    print("🤖 Houston HCCA Keyboard Automation")
    print("="*50)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        log_action(f"Running single command: {command}")
        send_command_method2(command)
    else:
        print("Choose mode:")
        print("1. Interactive mode")
        print("2. Test all commands automatically")
        
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == '1':
            interactive_mode()
        elif choice == '2':
            test_all_commands()
        else:
            log_action("Invalid choice. Starting interactive mode.")
            interactive_mode()

if __name__ == "__main__":
    main()
