*** Settings ***
Library    Process
Library    OperatingSystem
Library    BuiltIn
Library    Collections

*** Variables ***
${HS_DIR}                 C:\\Users\\<USER>\\Desktop\\SX-Houston-server_v142\\SX-Houston-server_v142
${HCCA_DIR}               C:\\Users\\<USER>\\Desktop\\SX-Houston-app_v212
${HS_PATH}                ${HS_DIR}\\houston_server.exe
${HCCA_PATH}              ${HCCA_DIR}\\houston_app.exe

*** Keywords ***
Kill Process If Running
    [Documentation]    Kills a process if it's running
    [Arguments]    ${process_name}
    Run Process    taskkill    /F    /IM    ${process_name}    /T    shell=True
    BuiltIn.Sleep    1s

Setup Environment
    [Documentation]    Starts both Houston Server and HCCA
    Kill Process If Running    houston_server.exe
    Kill Process If Running    houston_app.exe
    BuiltIn.Sleep    2s
    
    # Start Houston Server
    ${hs_handle}=    Start Process    ${HS_PATH}    shell=True    cwd=${HS_DIR}
    BuiltIn.Sleep    5s
    Log    ✅ Houston Server started    console=True
    
    # Start HCCA
    ${hcca_handle}=    Start Process    ${HCCA_PATH}    shell=True    cwd=${HCCA_DIR}
    BuiltIn.Sleep    5s
    Log    ✅ HCCA started    console=True
    
    RETURN    ${hs_handle}    ${hcca_handle}

Check Required Files
    [Documentation]    Checks if all required image files exist
    @{required_files}=    Create List    
    ...    search_box.png    
    ...    send_button.png    
    ...    get_app_period.png
    ...    search_field.png
    ...    set_time.png
    ...    reset_config.png
    ...    get_version.png
    
    ${missing_files}=    Create List
    
    FOR    ${file}    IN    @{required_files}
        ${file_path}=    Set Variable    ${CURDIR}\\${file}
        ${exists}=    Run Keyword And Return Status    OperatingSystem.File Should Exist    ${file_path}
        Run Keyword If    ${exists}    
        ...    Log    ✅ Found: ${file}    console=True
        ...    ELSE    Run Keywords
        ...    Log    ❌ Missing: ${file}    console=True    AND
        ...    Append To List    ${missing_files}    ${file}
    END
    
    ${missing_count}=    Get Length    ${missing_files}
    Run Keyword If    ${missing_count} > 0    
    ...    Log    ⚠️ ${missing_count} image files are missing. Please capture them before running the full test.    console=True
    ...    ELSE    Log    ✅ All required image files are present!    console=True
    
    RETURN    ${missing_files}

*** Test Cases ***
Basic Environment Test
    [Documentation]    Tests basic application startup without UI automation
    
    # Check required files first
    ${missing_files}=    Check Required Files
    
    # Setup environment
    ${hs_handle}    ${hcca_handle}=    Setup Environment
    
    # Wait a bit for applications to fully load
    BuiltIn.Sleep    10s
    Log    ⏳ Applications should now be running. Check them manually.    console=True
    Log    📝 This is a good time to capture missing images if needed.    console=True
    
    ${missing_count}=    Get Length    ${missing_files}
    Run Keyword If    ${missing_count} > 0    
    ...    Log    📸 Please capture these missing images: ${missing_files}    console=True
    
    # Keep applications running for 30 seconds for manual inspection
    Log    ⏰ Keeping applications running for 30 seconds for manual inspection...    console=True
    BuiltIn.Sleep    30s
    
    # Cleanup
    Log    🧹 Cleaning up - closing applications...    console=True
    Kill Process If Running    houston_server.exe
    Kill Process If Running    houston_app.exe
    
    Log    ✅ Basic environment test completed    console=True
