#!/usr/bin/env python3
"""
Image Capture Helper for Houston Test Automation
This script helps capture screenshots of UI elements for the Robot Framework tests.
"""

import pyautogui
import time
import os
import sys

def capture_screenshot(filename, description):
    """Capture a screenshot with user guidance"""
    print(f"\n{'='*60}")
    print(f"CAPTURING: {filename}")
    print(f"DESCRIPTION: {description}")
    print(f"{'='*60}")
    
    print("\nInstructions:")
    print("1. Make sure the Houston application is visible on screen")
    print("2. Navigate to the element you want to capture")
    print("3. Position your mouse cursor over the element")
    print("4. Press ENTER when ready to capture")
    
    input("Press ENTER when ready...")
    
    # Give user time to position
    print("Capturing in 3 seconds...")
    time.sleep(1)
    print("2...")
    time.sleep(1)
    print("1...")
    time.sleep(1)
    
    # Take screenshot
    screenshot = pyautogui.screenshot()
    
    # Get mouse position
    x, y = pyautogui.position()
    print(f"Mouse position: ({x}, {y})")
    
    # Ask user to define the area to capture
    print("\nNow we need to define the area to capture.")
    print("Move your mouse to the TOP-LEFT corner of the element and press ENTER")
    input("Press ENTER when positioned at TOP-LEFT...")
    
    x1, y1 = pyautogui.position()
    print(f"Top-left: ({x1}, {y1})")
    
    print("Now move your mouse to the BOTTOM-RIGHT corner of the element and press ENTER")
    input("Press ENTER when positioned at BOTTOM-RIGHT...")
    
    x2, y2 = pyautogui.position()
    print(f"Bottom-right: ({x2}, {y2})")
    
    # Crop the screenshot
    cropped = screenshot.crop((x1, y1, x2, y2))
    
    # Save the image
    filepath = os.path.join(os.getcwd(), filename)
    cropped.save(filepath)
    
    print(f"✅ Saved: {filepath}")
    print(f"Size: {cropped.size}")
    
    return filepath

def main():
    """Main function to capture all required images"""
    print("Houston Test Automation - Image Capture Helper")
    print("=" * 50)
    
    # List of images to capture
    images_to_capture = [
        ("search_field.png", "Empty search field/box where you type commands"),
        ("set_time.png", "The 'set_time' command in the search results"),
        ("reset_config.png", "The 'reset_config' command in the search results"),
        ("get_version.png", "The 'get_version' command in the search results")
    ]
    
    print("We need to capture 4 images for the test automation:")
    for i, (filename, desc) in enumerate(images_to_capture, 1):
        print(f"{i}. {filename} - {desc}")
    
    print("\nBefore we start:")
    print("1. Make sure Houston Server is running")
    print("2. Make sure Houston Control Center Application (HCCA) is running")
    print("3. Make sure the HCCA window is visible and accessible")
    
    input("\nPress ENTER when both applications are running and visible...")
    
    captured_files = []
    
    for filename, description in images_to_capture:
        try:
            filepath = capture_screenshot(filename, description)
            captured_files.append(filepath)
        except KeyboardInterrupt:
            print("\n\nCapture interrupted by user.")
            break
        except Exception as e:
            print(f"❌ Error capturing {filename}: {e}")
            continue
    
    print(f"\n{'='*60}")
    print("CAPTURE SUMMARY")
    print(f"{'='*60}")
    print(f"Successfully captured {len(captured_files)} images:")
    for filepath in captured_files:
        print(f"✅ {os.path.basename(filepath)}")
    
    print(f"\nImages saved in: {os.getcwd()}")
    print("You can now run the Robot Framework tests!")

if __name__ == "__main__":
    main()
