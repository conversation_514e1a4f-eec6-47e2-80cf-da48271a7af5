*** Settings ***
Documentation     Test suite for Houston Server and HCCA connection verification
Library           Process
Library           OperatingSystem
Library           DateTime
Library           String
Library           pyautogui

*** Variables ***
${HS_DIR}                 C:\\Users\\<USER>\\Desktop\\SX-Houston-server_v142\\SX-Houston-server_v142
${HCCA_DIR}               C:\\Users\\<USER>\\Desktop\\SX-Houston-app_v212
${HS_PATH}                ${HS_DIR}\\houston_server.exe
${HCCA_PATH}             ${HCCA_DIR}\\houston_app.exe
${HCCA_WINDOW_TITLE}      Houston Control Center Application
${TIMEOUT}                30s
${RETRY_INTERVAL}         2s
${RETRY_COUNT}            10

*** Keywords ***
Kill Process If Running
    [Documentation]    Kills a process if it's running
    [Arguments]    ${process_name}
    Run Process    taskkill    /F    /IM    ${process_name}    /T    shell=True
    Sleep    2s
    Log    ✅ Killed process: ${process_name}

Clean Environment
    [Documentation]    Cleans the environment before starting tests
    Kill Process If Running    houston_server.exe
    Kill Process If Running    houston_app.exe
    Log    🧹 Environment cleaned

Start Houston Server
    [Documentation]    Starts Houston Server and verifies it's running
    ${hs_handle}=    Start Process    ${HS_PATH}    shell=True    cwd=${HS_DIR}
    Sleep    10s
    Process Should Be Running    ${hs_handle}
    Log    🚀 Houston Server started successfully

Start HCCA
    [Documentation]    Starts HCCA application and verifies it's running
    ${hcca_handle}=    Start Process    ${HCCA_PATH}    shell=True    cwd=${HCCA_DIR}
    Sleep    5s
    Process Should Be Running    ${hcca_handle}
    Log    🚀 HCCA started successfully

Click Connect Button Placeholder
    [Documentation]    Placeholder for sending Connect interaction
    # You can implement UI control with pyautogui or RPA.Desktop
    Log    🖱️ Simulating click on Connect button (not implemented)

Verify Process Running
    [Arguments]    ${process_name}
    ${result}=    Run Process    tasklist    /FI    "IMAGENAME eq ${process_name}"    shell=True    stdout=TRUE
    ${stdout}=    Set Variable    ${result.stdout}
    Should Contain    ${stdout}    ${process_name}    Process ${process_name} is not running

Wait For Processes To Exit
    [Arguments]    ${hs_handle}    ${hcca_handle}
    Wait For Process    ${hs_handle}    timeout=${TIMEOUT}
    Wait For Process    ${hcca_handle}    timeout=${TIMEOUT}
    Log    ✅ Both applications exited normally after connection

*** Test Cases ***
Start And Connect Houston Applications
    [Documentation]    Main test case to verify Houston Server and HCCA connection
    [Setup]       Clean Environment
    ${hs_handle}=    Start Process    ${HS_PATH}    shell=True    cwd=${HS_DIR}
    Sleep    3s
    ${hcca_handle}=    Start Process    ${HCCA_PATH}    shell=True    cwd=${HCCA_DIR}
    Sleep    3s
    Click Connect Button Placeholder
    Wait For Processes To Exit    ${hs_handle}    ${hcca_handle}
    [Teardown]    Clean Environment

