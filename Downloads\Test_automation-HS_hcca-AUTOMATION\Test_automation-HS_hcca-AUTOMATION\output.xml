<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3 (Python 3.10.11 on win32)" generated="2025-06-03T09:49:28.946344" rpa="false" schemaversion="5">
<suite id="s1" name="Simple Test" source="C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\simple_test.robot">
<test id="s1-t1" name="Basic Environment Test" line="69">
<kw name="Check Required Files">
<kw name="Create List" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.000771" level="INFO">@{required_files} = [ search_box.png | send_button.png | get_app_period.png | search_field.png | set_time.png | reset_config.png | get_version.png ]</msg>
<var>@{required_files}</var>
<arg>search_box.png</arg>
<arg>send_button.png</arg>
<arg>get_app_period.png</arg>
<arg>search_field.png</arg>
<arg>set_time.png</arg>
<arg>reset_config.png</arg>
<arg>get_version.png</arg>
<doc>Returns a list containing given items.</doc>
<status status="PASS" start="2025-06-03T09:49:29.000771" elapsed="0.001000"/>
</kw>
<kw name="Create List" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.001771" level="INFO">${missing_files} = []</msg>
<var>${missing_files}</var>
<doc>Returns a list containing given items.</doc>
<status status="PASS" start="2025-06-03T09:49:29.001771" elapsed="0.000000"/>
</kw>
<for flavor="IN">
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.002770" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:49:29.001771" elapsed="0.000999"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:49:29.002770" level="FAIL">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png' does not exist.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="FAIL" start="2025-06-03T09:49:29.002770" elapsed="0.000000">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png' does not exist.</status>
</kw>
<msg time="2025-06-03T09:49:29.003770" level="INFO">${exists} = False</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:49:29.002770" elapsed="0.001000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.003770" level="INFO">❌ Missing: search_box.png</msg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:29.003770" elapsed="0.001326"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-03T09:49:29.005096" elapsed="0.000000"/>
</kw>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-06-03T09:49:29.003770" elapsed="0.001829"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:49:29.003770" elapsed="0.001829"/>
</kw>
<var name="${file}">search_box.png</var>
<status status="PASS" start="2025-06-03T09:49:29.001771" elapsed="0.003828"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.006113" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:49:29.005599" elapsed="0.000514"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:49:29.006113" level="INFO" html="true">File '&lt;a href="file://C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png"&gt;C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png&lt;/a&gt;' exists.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="PASS" start="2025-06-03T09:49:29.006113" elapsed="0.001008"/>
</kw>
<msg time="2025-06-03T09:49:29.007121" level="INFO">${exists} = True</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:49:29.006113" elapsed="0.001008"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.007875" level="INFO">✅ Found: send_button.png</msg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:29.007121" elapsed="0.001028"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:49:29.007121" elapsed="0.001028"/>
</kw>
<var name="${file}">send_button.png</var>
<status status="PASS" start="2025-06-03T09:49:29.005599" elapsed="0.002550"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.008149" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:49:29.008149" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:49:29.009120" level="FAIL">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png' does not exist.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="FAIL" start="2025-06-03T09:49:29.009120" elapsed="0.000000">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png' does not exist.</status>
</kw>
<msg time="2025-06-03T09:49:29.010117" level="INFO">${exists} = False</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:49:29.009120" elapsed="0.000997"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.010117" level="INFO">❌ Missing: get_app_period.png</msg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:29.010117" elapsed="0.000997"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-03T09:49:29.011114" elapsed="0.000000"/>
</kw>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-06-03T09:49:29.010117" elapsed="0.000997"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:49:29.010117" elapsed="0.001993"/>
</kw>
<var name="${file}">get_app_period.png</var>
<status status="PASS" start="2025-06-03T09:49:29.008149" elapsed="0.003961"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.012110" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_field.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:49:29.012110" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:49:29.014149" level="INFO" html="true">File '&lt;a href="file://C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_field.png"&gt;C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_field.png&lt;/a&gt;' exists.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="PASS" start="2025-06-03T09:49:29.013112" elapsed="0.001037"/>
</kw>
<msg time="2025-06-03T09:49:29.014149" level="INFO">${exists} = True</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:49:29.013112" elapsed="0.001037"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.015142" level="INFO">✅ Found: search_field.png</msg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:29.014149" elapsed="0.001429"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:49:29.014149" elapsed="0.001429"/>
</kw>
<var name="${file}">search_field.png</var>
<status status="PASS" start="2025-06-03T09:49:29.012110" elapsed="0.003468"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.016107" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:49:29.016107" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:49:29.018158" level="FAIL">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png' does not exist.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="FAIL" start="2025-06-03T09:49:29.017147" elapsed="0.001011">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png' does not exist.</status>
</kw>
<msg time="2025-06-03T09:49:29.018158" level="INFO">${exists} = False</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:49:29.016107" elapsed="0.002051"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.020160" level="INFO">❌ Missing: set_time.png</msg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:29.019160" elapsed="0.001312"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-03T09:49:29.021109" elapsed="0.000000"/>
</kw>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-06-03T09:49:29.019160" elapsed="0.001949"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:49:29.018158" elapsed="0.002951"/>
</kw>
<var name="${file}">set_time.png</var>
<status status="PASS" start="2025-06-03T09:49:29.016107" elapsed="0.005002"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.022144" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:49:29.022144" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:49:29.024159" level="FAIL">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png' does not exist.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="FAIL" start="2025-06-03T09:49:29.023143" elapsed="0.001016">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png' does not exist.</status>
</kw>
<msg time="2025-06-03T09:49:29.024159" level="INFO">${exists} = False</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:49:29.023143" elapsed="0.001016"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.027111" level="INFO">❌ Missing: reset_config.png</msg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:29.026160" elapsed="0.000951"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-03T09:49:29.028105" elapsed="0.000000"/>
</kw>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-06-03T09:49:29.026160" elapsed="0.001945"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:49:29.025158" elapsed="0.003948"/>
</kw>
<var name="${file}">reset_config.png</var>
<status status="PASS" start="2025-06-03T09:49:29.022144" elapsed="0.006962"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.031142" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:49:29.030337" elapsed="0.000805"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:49:29.032491" level="INFO" html="true">File '&lt;a href="file://C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png"&gt;C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png&lt;/a&gt;' exists.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="PASS" start="2025-06-03T09:49:29.031142" elapsed="0.001969"/>
</kw>
<msg time="2025-06-03T09:49:29.033111" level="INFO">${exists} = True</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:49:29.031142" elapsed="0.001969"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.035101" level="INFO">✅ Found: get_version.png</msg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:29.034181" elapsed="0.000920"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:49:29.033111" elapsed="0.001990"/>
</kw>
<var name="${file}">get_version.png</var>
<status status="PASS" start="2025-06-03T09:49:29.030337" elapsed="0.005761"/>
</iter>
<var>${file}</var>
<value>@{required_files}</value>
<status status="PASS" start="2025-06-03T09:49:29.001771" elapsed="0.034327"/>
</for>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.037097" level="INFO">Length is 4.</msg>
<msg time="2025-06-03T09:49:29.037097" level="INFO">${missing_count} = 4</msg>
<var>${missing_count}</var>
<arg>${missing_files}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-03T09:49:29.036098" elapsed="0.000999"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:29.039098" level="INFO">⚠️ 4 image files are missing. Please capture them before running the full test.</msg>
<arg>⚠️ ${missing_count} image files are missing. Please capture them before running the full test.</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:29.038095" elapsed="0.001003"/>
</kw>
<arg>${missing_count} &gt; 0</arg>
<arg>Log</arg>
<arg>⚠️ ${missing_count} image files are missing. Please capture them before running the full test.</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>✅ All required image files are present!</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:49:29.037097" elapsed="0.003066"/>
</kw>
<return>
<value>${missing_files}</value>
<status status="PASS" start="2025-06-03T09:49:29.040163" elapsed="0.000000"/>
</return>
<msg time="2025-06-03T09:49:29.041102" level="INFO">${missing_files} = ['search_box.png', 'get_app_period.png', 'set_time.png', 'reset_config.png']</msg>
<var>${missing_files}</var>
<doc>Checks if all required image files exist</doc>
<status status="PASS" start="2025-06-03T09:49:28.999768" elapsed="0.041334"/>
</kw>
<kw name="Setup Environment">
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-03T09:49:29.043114" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-03T09:49:29.052085" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-03T09:49:29.338708" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-03T09:49:29.042098" elapsed="0.297610"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:49:30.355525" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:49:29.339930" elapsed="1.016224"/>
</kw>
<arg>houston_server.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-03T09:49:29.042098" elapsed="1.314470"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-03T09:49:30.360555" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-03T09:49:30.381754" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-03T09:49:30.729388" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-03T09:49:30.359560" elapsed="0.369828"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:49:31.740807" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:49:30.730377" elapsed="1.011010"/>
</kw>
<arg>houston_app.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-03T09:49:30.357564" elapsed="1.384356"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:49:33.753620" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:49:31.742763" elapsed="2.010857"/>
</kw>
<kw name="Start Process" owner="Process">
<msg time="2025-06-03T09:49:33.756495" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-server_v142\SX-Houston-server_v142\houston_server.exe</msg>
<msg time="2025-06-03T09:49:33.779486" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<arg>${HS_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HS_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-03T09:49:33.754496" elapsed="0.024990"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:49:38.795765" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:49:33.780480" elapsed="5.015285"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:38.797835" level="INFO">✅ Houston Server started</msg>
<arg>✅ Houston Server started</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:38.795765" elapsed="0.003245"/>
</kw>
<kw name="Start Process" owner="Process">
<msg time="2025-06-03T09:49:38.802009" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-app_v212\houston_app.exe</msg>
<msg time="2025-06-03T09:49:38.817293" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hcca_handle}</var>
<arg>${HCCA_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HCCA_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-03T09:49:38.800584" elapsed="0.017280"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:49:43.830627" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:49:38.818425" elapsed="5.012202"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:43.832488" level="INFO">✅ HCCA started</msg>
<arg>✅ HCCA started</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:43.831488" elapsed="0.002099"/>
</kw>
<return>
<value>${hs_handle}</value>
<value>${hcca_handle}</value>
<status status="PASS" start="2025-06-03T09:49:43.833587" elapsed="0.000903"/>
</return>
<msg time="2025-06-03T09:49:43.835489" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<msg time="2025-06-03T09:49:43.835489" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<var>${hcca_handle}</var>
<doc>Starts both Houston Server and HCCA</doc>
<status status="PASS" start="2025-06-03T09:49:29.041396" elapsed="14.794093"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:49:53.843419" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:49:43.836880" elapsed="10.006539"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:53.845036" level="INFO">⏳ Applications should now be running. Check them manually.</msg>
<arg>⏳ Applications should now be running. Check them manually.</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:53.845036" elapsed="0.001060"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:53.848035" level="INFO">📝 This is a good time to capture missing images if needed.</msg>
<arg>📝 This is a good time to capture missing images if needed.</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:53.848035" elapsed="0.001048"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-03T09:49:53.851037" level="INFO">Length is 4.</msg>
<msg time="2025-06-03T09:49:53.852096" level="INFO">${missing_count} = 4</msg>
<var>${missing_count}</var>
<arg>${missing_files}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-03T09:49:53.850038" elapsed="0.002058"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:53.856038" level="INFO">📸 Please capture these missing images: ['search_box.png', 'get_app_period.png', 'set_time.png', 'reset_config.png']</msg>
<arg>📸 Please capture these missing images: ${missing_files}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:53.855039" elapsed="0.002062"/>
</kw>
<arg>${missing_count} &gt; 0</arg>
<arg>Log</arg>
<arg>📸 Please capture these missing images: ${missing_files}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:49:53.853050" elapsed="0.005048"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:49:53.861030" level="INFO">⏰ Keeping applications running for 30 seconds for manual inspection...</msg>
<arg>⏰ Keeping applications running for 30 seconds for manual inspection...</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:49:53.859039" elapsed="0.002988"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:50:23.880269" level="INFO">Slept 30 seconds.</msg>
<arg>30s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:49:53.864032" elapsed="30.016237"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:50:23.885271" level="INFO">🧹 Cleaning up - closing applications...</msg>
<arg>🧹 Cleaning up - closing applications...</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:50:23.883277" elapsed="0.004035"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-03T09:50:23.896545" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-03T09:50:23.919895" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-03T09:50:24.332577" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-03T09:50:23.894548" elapsed="0.439037"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:50:25.347693" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:50:24.333585" elapsed="1.014108"/>
</kw>
<arg>houston_server.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-03T09:50:23.892286" elapsed="1.455407"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-03T09:50:25.349648" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-03T09:50:25.355646" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-03T09:50:25.492047" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-03T09:50:25.348646" elapsed="0.143401"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:50:26.505454" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:50:25.492647" elapsed="1.012807"/>
</kw>
<arg>houston_app.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-03T09:50:25.348646" elapsed="1.156808"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:50:26.507475" level="INFO">✅ Basic environment test completed</msg>
<arg>✅ Basic environment test completed</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:50:26.506458" elapsed="0.002002"/>
</kw>
<doc>Tests basic application startup without UI automation</doc>
<status status="PASS" start="2025-06-03T09:49:28.998769" elapsed="57.509691"/>
</test>
<status status="PASS" start="2025-06-03T09:49:28.948836" elapsed="57.563672"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Simple Test" id="s1" pass="1" fail="0" skip="0">Simple Test</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
