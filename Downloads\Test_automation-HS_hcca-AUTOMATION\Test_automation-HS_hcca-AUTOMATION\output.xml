<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3 (Python 3.10.11 on win32)" generated="2025-06-02T16:21:16.618621" rpa="false" schemaversion="5">
<suite id="s1" name="Get" source="C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get.robot">
<test id="s1-t1" name="Send All Commands" line="130">
<kw name="Setup Environment">
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:21:16.886231" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-02T16:21:16.890228" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:21:17.015749" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:21:16.885232" elapsed="0.130517"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:18.024558" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:17.016773" elapsed="1.007785"/>
</kw>
<arg>houston_server.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:21:16.884231" elapsed="1.141386"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:21:18.029654" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-02T16:21:18.046595" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:21:18.176355" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:21:18.027605" elapsed="0.148750"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:19.182122" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:18.176355" elapsed="1.005767"/>
</kw>
<arg>houston_app.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:21:18.026606" elapsed="1.155516"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:21.186882" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:19.182122" elapsed="2.005796"/>
</kw>
<kw name="Start Process" owner="Process">
<msg time="2025-06-02T16:21:21.189915" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-server_v142\SX-Houston-server_v142\houston_server.exe</msg>
<msg time="2025-06-02T16:21:21.209964" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<arg>${HS_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HS_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-02T16:21:21.187918" elapsed="0.022046"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:26.220430" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:21.211035" elapsed="5.009395"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:26.221459" level="INFO">✅ Houston Server started</msg>
<arg>✅ Houston Server started</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:26.221459" elapsed="0.001038"/>
</kw>
<kw name="Start Process" owner="Process">
<msg time="2025-06-02T16:21:26.224482" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-app_v212\houston_app.exe</msg>
<msg time="2025-06-02T16:21:26.239451" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hcca_handle}</var>
<arg>${HCCA_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HCCA_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-02T16:21:26.222497" elapsed="0.017956"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:31.253168" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:26.240453" elapsed="5.013713"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:31.255173" level="INFO">✅ HCCA started</msg>
<arg>✅ HCCA started</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:31.254166" elapsed="0.002007"/>
</kw>
<return>
<value>${hs_handle}</value>
<value>${hcca_handle}</value>
<status status="PASS" start="2025-06-02T16:21:31.257158" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:31.259167" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<msg time="2025-06-02T16:21:31.259167" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<var>${hcca_handle}</var>
<doc>Starts both Houston Server and HCCA</doc>
<status status="PASS" start="2025-06-02T16:21:16.884231" elapsed="14.374936"/>
</kw>
<kw name="Create List" owner="BuiltIn">
<msg time="2025-06-02T16:21:31.262230" level="INFO">@{commands} = [ get_app_period | set_time | reset_config | get_version ]</msg>
<var>@{commands}</var>
<arg>get_app_period</arg>
<arg>set_time</arg>
<arg>reset_config</arg>
<arg>get_version</arg>
<doc>Returns a list containing given items.</doc>
<status status="PASS" start="2025-06-02T16:21:31.261227" elapsed="0.001003"/>
</kw>
<for flavor="IN">
<iter>
<kw name="Send Command">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:31.267227" level="INFO">Attempting to send command: get_app_period</msg>
<arg>Attempting to send command: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:31.266229" elapsed="0.002928"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:31.275194" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:31.274172" elapsed="0.001022"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="PASS" start="2025-06-02T16:21:31.276152" elapsed="0.098779"/>
</kw>
<msg time="2025-06-02T16:21:31.374931" level="INFO">${location} = True</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:31.276152" elapsed="0.098779"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:31.375932" elapsed="0.000000"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:31.374931" elapsed="0.001001"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:31.375932" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:31.375932" level="INFO">${search_box_found} = True</msg>
<var>${search_box_found}</var>
<arg>${SEARCH_BOX_IMAGE}</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:31.272161" elapsed="0.103771"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Typewrite" owner="pyautogui">
<arg>${command_name}</arg>
<doc>Performs a keyboard key press down, followed by a release, for each of
the characters in message.</doc>
<status status="PASS" start="2025-06-02T16:21:31.376931" elapsed="0.113642"/>
</kw>
<arg>${search_box_found}</arg>
<arg>pyautogui.typewrite</arg>
<arg>${command_name}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>⚠️ Search box not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:31.375932" elapsed="0.114641"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:32.492637" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:31.491408" elapsed="1.002004"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:21:32.495463" level="INFO">${command_image} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png</msg>
<var>${command_image}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${command_name}.png</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:21:32.493412" elapsed="0.002051"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:32.499247" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:32.498415" elapsed="0.001830"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<msg time="2025-06-02T16:21:32.598947" level="FAIL">ImageNotFoundException</msg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="FAIL" start="2025-06-02T16:21:32.501236" elapsed="0.099276">ImageNotFoundException</status>
</kw>
<msg time="2025-06-02T16:21:32.600512" level="INFO">${location} = False</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:32.501236" elapsed="0.099276"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click" owner="pyautogui">
<arg>${location}</arg>
<doc>Performs pressing a mouse button down and then immediately releasing it. Returns ``None``.</doc>
<status status="PASS" start="2025-06-02T16:21:32.600512" elapsed="0.112750"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:32.600512" elapsed="0.112750"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:32.713796" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:32.713796" level="INFO">${command_found} = False</msg>
<var>${command_found}</var>
<arg>${command_image}</arg>
<arg>double_click=True</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:32.496409" elapsed="0.217387"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:32.716795" level="INFO">⚠️ Command not found: get_app_period</msg>
<arg>⚠️ Command not found: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:32.715793" elapsed="0.001002"/>
</kw>
<arg>not ${command_found}</arg>
<arg>Log</arg>
<arg>⚠️ Command not found: ${command_name}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:32.714791" elapsed="0.002004"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:33.719037" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:32.717795" elapsed="1.001242"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:33.722106" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:33.721060" elapsed="0.001046"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="PASS" start="2025-06-02T16:21:33.723054" elapsed="0.090660"/>
</kw>
<msg time="2025-06-02T16:21:33.813714" level="INFO">${location} = True</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:33.723054" elapsed="0.090660"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:33.813714" elapsed="0.000000"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:33.813714" elapsed="0.000000"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:33.814713" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:33.814713" level="INFO">${send_button_found} = True</msg>
<var>${send_button_found}</var>
<arg>${SEND_BUTTON_IMAGE}</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:33.720052" elapsed="0.094661"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>not ${send_button_found}</arg>
<arg>Log</arg>
<arg>⚠️ Send button not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:33.814713" elapsed="0.000976"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-02T16:21:33.815689" level="INFO">${success} = False</msg>
<var>${success}</var>
<arg>${search_box_found} and ${command_found} and ${send_button_found}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-02T16:21:33.815689" elapsed="0.000000"/>
</kw>
<return>
<value>${success}</value>
<status status="PASS" start="2025-06-02T16:21:33.815689" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:33.815689" level="INFO">${cmd_success} = False</msg>
<var>${cmd_success}</var>
<arg>${cmd}</arg>
<doc>Sends a command using PyAutoGUI</doc>
<status status="PASS" start="2025-06-02T16:21:31.265228" elapsed="2.550461"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:33.817713" level="INFO">⚠️ Failed to send command: get_app_period</msg>
<arg>⚠️ Failed to send command: ${cmd}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:33.816713" elapsed="0.001000"/>
</kw>
<arg>not ${cmd_success}</arg>
<arg>Log</arg>
<arg>⚠️ Failed to send command: ${cmd}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>✅ Successfully sent command: ${cmd}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:33.815689" elapsed="0.002024"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:35.820458" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:33.817713" elapsed="2.002745"/>
</kw>
<var name="${cmd}">get_app_period</var>
<status status="PASS" start="2025-06-02T16:21:31.264233" elapsed="4.557009"/>
</iter>
<iter>
<kw name="Send Command">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:35.823296" level="INFO">Attempting to send command: set_time</msg>
<arg>Attempting to send command: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:35.822247" elapsed="0.001997"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:35.826294" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:35.825245" elapsed="0.001997"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="PASS" start="2025-06-02T16:21:35.827242" elapsed="0.085949"/>
</kw>
<msg time="2025-06-02T16:21:35.913191" level="INFO">${location} = True</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:35.827242" elapsed="0.085949"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:35.913191" elapsed="0.000000"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:35.913191" elapsed="0.001002"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:35.914193" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:35.914193" level="INFO">${search_box_found} = True</msg>
<var>${search_box_found}</var>
<arg>${SEARCH_BOX_IMAGE}</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:35.824244" elapsed="0.089949"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Typewrite" owner="pyautogui">
<arg>${command_name}</arg>
<doc>Performs a keyboard key press down, followed by a release, for each of
the characters in message.</doc>
<status status="PASS" start="2025-06-02T16:21:35.914193" elapsed="0.108656"/>
</kw>
<arg>${search_box_found}</arg>
<arg>pyautogui.typewrite</arg>
<arg>${command_name}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>⚠️ Search box not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:35.914193" elapsed="0.108656"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:37.035161" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:36.023686" elapsed="1.011475"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:21:37.036127" level="INFO">${command_image} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png</msg>
<var>${command_image}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${command_name}.png</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:21:37.035161" elapsed="0.000966"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:37.037161" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:37.037161" elapsed="0.000999"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<msg time="2025-06-02T16:21:37.071510" level="FAIL">OSError: Failed to read C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png because file is missing, has improper permissions, or is an unsupported or invalid format</msg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="FAIL" start="2025-06-02T16:21:37.038160" elapsed="0.034353">OSError: Failed to read C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png because file is missing, has improper permissions, or is an unsupported or invalid format</status>
</kw>
<msg time="2025-06-02T16:21:37.072513" level="INFO">${location} = False</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:37.038160" elapsed="0.034353"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click" owner="pyautogui">
<arg>${location}</arg>
<doc>Performs pressing a mouse button down and then immediately releasing it. Returns ``None``.</doc>
<status status="PASS" start="2025-06-02T16:21:37.073549" elapsed="0.118366"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:37.072513" elapsed="0.119402"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:37.191915" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:37.192934" level="INFO">${command_found} = False</msg>
<var>${command_found}</var>
<arg>${command_image}</arg>
<arg>double_click=True</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:37.036127" elapsed="0.156807"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:37.193933" level="INFO">⚠️ Command not found: set_time</msg>
<arg>⚠️ Command not found: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:37.193933" elapsed="0.001007"/>
</kw>
<arg>not ${command_found}</arg>
<arg>Log</arg>
<arg>⚠️ Command not found: ${command_name}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:37.192934" elapsed="0.002006"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:38.208135" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:37.194940" elapsed="1.013195"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:38.210141" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:38.210141" elapsed="0.000971"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="PASS" start="2025-06-02T16:21:38.212119" elapsed="0.102359"/>
</kw>
<msg time="2025-06-02T16:21:38.314478" level="INFO">${location} = True</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:38.211112" elapsed="0.103366"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:38.314478" elapsed="0.000984"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:38.314478" elapsed="0.000984"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:38.315462" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:38.315462" level="INFO">${send_button_found} = True</msg>
<var>${send_button_found}</var>
<arg>${SEND_BUTTON_IMAGE}</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:38.209116" elapsed="0.106346"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>not ${send_button_found}</arg>
<arg>Log</arg>
<arg>⚠️ Send button not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:38.315462" elapsed="0.000990"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-02T16:21:38.317458" level="INFO">${success} = False</msg>
<var>${success}</var>
<arg>${search_box_found} and ${command_found} and ${send_button_found}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-02T16:21:38.316452" elapsed="0.001006"/>
</kw>
<return>
<value>${success}</value>
<status status="PASS" start="2025-06-02T16:21:38.317458" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:38.317458" level="INFO">${cmd_success} = False</msg>
<var>${cmd_success}</var>
<arg>${cmd}</arg>
<doc>Sends a command using PyAutoGUI</doc>
<status status="PASS" start="2025-06-02T16:21:35.822247" elapsed="2.495211"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:38.318451" level="INFO">⚠️ Failed to send command: set_time</msg>
<arg>⚠️ Failed to send command: ${cmd}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:38.318451" elapsed="0.000000"/>
</kw>
<arg>not ${cmd_success}</arg>
<arg>Log</arg>
<arg>⚠️ Failed to send command: ${cmd}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>✅ Successfully sent command: ${cmd}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:38.317458" elapsed="0.000993"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:40.323539" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:38.318451" elapsed="2.005088"/>
</kw>
<var name="${cmd}">set_time</var>
<status status="PASS" start="2025-06-02T16:21:35.821242" elapsed="4.502297"/>
</iter>
<iter>
<kw name="Send Command">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:40.325539" level="INFO">Attempting to send command: reset_config</msg>
<arg>Attempting to send command: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:40.325539" elapsed="0.006001"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:40.332539" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>