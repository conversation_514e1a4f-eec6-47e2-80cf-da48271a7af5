<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3 (Python 3.10.11 on win32)" generated="2025-06-02T16:28:19.640814" rpa="false" schemaversion="5">
<suite id="s1" name="Simple Test" source="C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\simple_test.robot">
<test id="s1-t1" name="Basic Environment Test" line="69">
<kw name="Check Required Files">
<kw name="Create List" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.693842" level="INFO">@{required_files} = [ search_box.png | send_button.png | get_app_period.png | search_field.png | set_time.png | reset_config.png | get_version.png ]</msg>
<var>@{required_files}</var>
<arg>search_box.png</arg>
<arg>send_button.png</arg>
<arg>get_app_period.png</arg>
<arg>search_field.png</arg>
<arg>set_time.png</arg>
<arg>reset_config.png</arg>
<arg>get_version.png</arg>
<doc>Returns a list containing given items.</doc>
<status status="PASS" start="2025-06-02T16:28:19.692846" elapsed="0.000996"/>
</kw>
<kw name="Create List" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.693842" level="INFO">${missing_files} = []</msg>
<var>${missing_files}</var>
<doc>Returns a list containing given items.</doc>
<status status="PASS" start="2025-06-02T16:28:19.693842" elapsed="0.000000"/>
</kw>
<for flavor="IN">
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.694844" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:28:19.693842" elapsed="0.001002"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-02T16:28:19.694844" level="INFO" html="true">File '&lt;a href="file://C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png"&gt;C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png&lt;/a&gt;' exists.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="PASS" start="2025-06-02T16:28:19.694844" elapsed="0.000000"/>
</kw>
<msg time="2025-06-02T16:28:19.694844" level="INFO">${exists} = True</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:28:19.694844" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.695845" level="INFO">✅ Found: search_box.png</msg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:19.695845" elapsed="0.000000"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:28:19.695845" elapsed="0.000000"/>
</kw>
<var name="${file}">search_box.png</var>
<status status="PASS" start="2025-06-02T16:28:19.693842" elapsed="0.002003"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.696810" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:28:19.696810" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-02T16:28:19.697813" level="INFO" html="true">File '&lt;a href="file://C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png"&gt;C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png&lt;/a&gt;' exists.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="PASS" start="2025-06-02T16:28:19.697813" elapsed="0.000000"/>
</kw>
<msg time="2025-06-02T16:28:19.697813" level="INFO">${exists} = True</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:28:19.696810" elapsed="0.001003"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.698818" level="INFO">✅ Found: send_button.png</msg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:19.698818" elapsed="0.000000"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:28:19.697813" elapsed="0.001005"/>
</kw>
<var name="${file}">send_button.png</var>
<status status="PASS" start="2025-06-02T16:28:19.696810" elapsed="0.003000"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.699810" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:28:19.699810" elapsed="0.001016"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-02T16:28:19.701813" level="INFO" html="true">File '&lt;a href="file://C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png"&gt;C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png&lt;/a&gt;' exists.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="PASS" start="2025-06-02T16:28:19.700826" elapsed="0.000987"/>
</kw>
<msg time="2025-06-02T16:28:19.701813" level="INFO">${exists} = True</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:28:19.700826" elapsed="0.000987"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.702813" level="INFO">✅ Found: get_app_period.png</msg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:19.702813" elapsed="0.000000"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:28:19.701813" elapsed="0.001000"/>
</kw>
<var name="${file}">get_app_period.png</var>
<status status="PASS" start="2025-06-02T16:28:19.699810" elapsed="0.004003"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.703813" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_field.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:28:19.703813" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-02T16:28:19.705814" level="FAIL">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_field.png' does not exist.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="FAIL" start="2025-06-02T16:28:19.704811" elapsed="0.001003">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_field.png' does not exist.</status>
</kw>
<msg time="2025-06-02T16:28:19.705814" level="INFO">${exists} = False</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:28:19.704811" elapsed="0.001003"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.707813" level="INFO">❌ Missing: search_field.png</msg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:19.707813" elapsed="0.000000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-02T16:28:19.708810" elapsed="0.000000"/>
</kw>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-06-02T16:28:19.706820" elapsed="0.001990"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:28:19.705814" elapsed="0.002996"/>
</kw>
<var name="${file}">search_field.png</var>
<status status="PASS" start="2025-06-02T16:28:19.703813" elapsed="0.004997"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.709809" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:28:19.708810" elapsed="0.000999"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-02T16:28:19.709809" level="FAIL">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png' does not exist.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="FAIL" start="2025-06-02T16:28:19.709809" elapsed="0.000000">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png' does not exist.</status>
</kw>
<msg time="2025-06-02T16:28:19.710813" level="INFO">${exists} = False</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:28:19.709809" elapsed="0.001004"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.710813" level="INFO">❌ Missing: set_time.png</msg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:19.710813" elapsed="0.001000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-02T16:28:19.711813" elapsed="0.000000"/>
</kw>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-06-02T16:28:19.710813" elapsed="0.001000"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:28:19.710813" elapsed="0.002001"/>
</kw>
<var name="${file}">set_time.png</var>
<status status="PASS" start="2025-06-02T16:28:19.708810" elapsed="0.004004"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.712814" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:28:19.712814" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-02T16:28:19.713813" level="FAIL">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png' does not exist.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="FAIL" start="2025-06-02T16:28:19.713813" elapsed="0.000000">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png' does not exist.</status>
</kw>
<msg time="2025-06-02T16:28:19.713813" level="INFO">${exists} = False</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:28:19.712814" elapsed="0.000999"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.714813" level="INFO">❌ Missing: reset_config.png</msg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:19.714813" elapsed="0.000997"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-02T16:28:19.715810" elapsed="0.000000"/>
</kw>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-06-02T16:28:19.714813" elapsed="0.000997"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:28:19.714813" elapsed="0.000997"/>
</kw>
<var name="${file}">reset_config.png</var>
<status status="PASS" start="2025-06-02T16:28:19.712814" elapsed="0.002996"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.716813" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:28:19.716813" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-02T16:28:19.717812" level="FAIL">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png' does not exist.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="FAIL" start="2025-06-02T16:28:19.716813" elapsed="0.000999">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png' does not exist.</status>
</kw>
<msg time="2025-06-02T16:28:19.717812" level="INFO">${exists} = False</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:28:19.716813" elapsed="0.000999"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.718814" level="INFO">❌ Missing: get_version.png</msg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:19.718814" elapsed="0.000996"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-02T16:28:19.719810" elapsed="0.000000"/>
</kw>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-06-02T16:28:19.718814" elapsed="0.000996"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:28:19.718814" elapsed="0.000996"/>
</kw>
<var name="${file}">get_version.png</var>
<status status="PASS" start="2025-06-02T16:28:19.716813" elapsed="0.002997"/>
</iter>
<var>${file}</var>
<value>@{required_files}</value>
<status status="PASS" start="2025-06-02T16:28:19.693842" elapsed="0.025968"/>
</for>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.720812" level="INFO">Length is 4.</msg>
<msg time="2025-06-02T16:28:19.720812" level="INFO">${missing_count} = 4</msg>
<var>${missing_count}</var>
<arg>${missing_files}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-02T16:28:19.720812" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:19.721813" level="INFO">⚠️ 4 image files are missing. Please capture them before running the full test.</msg>
<arg>⚠️ ${missing_count} image files are missing. Please capture them before running the full test.</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:19.721813" elapsed="0.001998"/>
</kw>
<arg>${missing_count} &gt; 0</arg>
<arg>Log</arg>
<arg>⚠️ ${missing_count} image files are missing. Please capture them before running the full test.</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>✅ All required image files are present!</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:28:19.720812" elapsed="0.002999"/>
</kw>
<return>
<value>${missing_files}</value>
<status status="PASS" start="2025-06-02T16:28:19.723811" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:28:19.724811" level="INFO">${missing_files} = ['search_field.png', 'set_time.png', 'reset_config.png', 'get_version.png']</msg>
<var>${missing_files}</var>
<doc>Checks if all required image files exist</doc>
<status status="PASS" start="2025-06-02T16:28:19.692846" elapsed="0.031965"/>
</kw>
<kw name="Setup Environment">
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:28:19.726814" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-02T16:28:19.732812" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:28:19.862229" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:28:19.725814" elapsed="0.136415"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:28:20.875350" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:28:19.863228" elapsed="1.012122"/>
</kw>
<arg>houston_server.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:28:19.725814" elapsed="1.149536"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:28:20.879364" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-02T16:28:20.894800" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:28:21.055254" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:28:20.878363" elapsed="0.176891"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:28:22.057642" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:28:21.055254" elapsed="1.002388"/>
</kw>
<arg>houston_app.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:28:20.876366" elapsed="1.181276"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:28:24.073121" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:28:22.058151" elapsed="2.014970"/>
</kw>
<kw name="Start Process" owner="Process">
<msg time="2025-06-02T16:28:24.075179" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-server_v142\SX-Houston-server_v142\houston_server.exe</msg>
<msg time="2025-06-02T16:28:24.085134" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<arg>${HS_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HS_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-02T16:28:24.074135" elapsed="0.012044"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:28:29.098195" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:28:24.086179" elapsed="5.012016"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:29.099201" level="INFO">✅ Houston Server started</msg>
<arg>✅ Houston Server started</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:29.099201" elapsed="0.001350"/>
</kw>
<kw name="Start Process" owner="Process">
<msg time="2025-06-02T16:28:29.101586" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-app_v212\houston_app.exe</msg>
<msg time="2025-06-02T16:28:29.117440" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hcca_handle}</var>
<arg>${HCCA_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HCCA_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-02T16:28:29.100551" elapsed="0.016889"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:28:34.123428" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:28:29.118438" elapsed="5.004990"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:34.124205" level="INFO">✅ HCCA started</msg>
<arg>✅ HCCA started</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:34.124205" elapsed="0.000998"/>
</kw>
<return>
<value>${hs_handle}</value>
<value>${hcca_handle}</value>
<status status="PASS" start="2025-06-02T16:28:34.125203" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:28:34.126210" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<msg time="2025-06-02T16:28:34.127211" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<var>${hcca_handle}</var>
<doc>Starts both Houston Server and HCCA</doc>
<status status="PASS" start="2025-06-02T16:28:19.724811" elapsed="14.402400"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:28:44.132881" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:28:34.127634" elapsed="10.005247"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:44.133884" level="INFO">⏳ Applications should now be running. Check them manually.</msg>
<arg>⏳ Applications should now be running. Check them manually.</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:44.132881" elapsed="0.001003"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:44.133884" level="INFO">📝 This is a good time to capture missing images if needed.</msg>
<arg>📝 This is a good time to capture missing images if needed.</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:44.133884" elapsed="0.000999"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-02T16:28:44.134883" level="INFO">Length is 4.</msg>
<msg time="2025-06-02T16:28:44.134883" level="INFO">${missing_count} = 4</msg>
<var>${missing_count}</var>
<arg>${missing_files}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-02T16:28:44.134883" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:44.135920" level="INFO">📸 Please capture these missing images: ['search_field.png', 'set_time.png', 'reset_config.png', 'get_version.png']</msg>
<arg>📸 Please capture these missing images: ${missing_files}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:44.135920" elapsed="0.000000"/>
</kw>
<arg>${missing_count} &gt; 0</arg>
<arg>Log</arg>
<arg>📸 Please capture these missing images: ${missing_files}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:28:44.134883" elapsed="0.001037"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:28:44.136885" level="INFO">⏰ Keeping applications running for 30 seconds for manual inspection...</msg>
<arg>⏰ Keeping applications running for 30 seconds for manual inspection...</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:28:44.136885" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:29:14.147951" level="INFO">Slept 30 seconds.</msg>
<arg>30s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:28:44.136885" elapsed="30.011066"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:29:14.149721" level="INFO">🧹 Cleaning up - closing applications...</msg>
<arg>🧹 Cleaning up - closing applications...</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:29:14.148722" elapsed="0.002002"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:29:14.152774" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-02T16:29:14.161767" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:29:14.300199" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:29:14.151776" elapsed="0.148423"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:29:15.304963" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:29:14.301200" elapsed="1.004028"/>
</kw>
<arg>houston_server.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:29:14.150724" elapsed="1.155019"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:29:15.308761" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-02T16:29:15.315798" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:29:15.440286" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:29:15.307762" elapsed="0.132524"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:29:16.449092" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:29:15.441288" elapsed="1.007804"/>
</kw>
<arg>houston_app.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:29:15.306761" elapsed="1.142331"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:29:16.450093" level="INFO">✅ Basic environment test completed</msg>
<arg>✅ Basic environment test completed</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:29:16.449092" elapsed="0.001001"/>
</kw>
<doc>Tests basic application startup without UI automation</doc>
<status status="PASS" start="2025-06-02T16:28:19.691814" elapsed="56.758279"/>
</test>
<status status="PASS" start="2025-06-02T16:28:19.643813" elapsed="56.807279"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Simple Test" id="s1" pass="1" fail="0" skip="0">Simple Test</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
