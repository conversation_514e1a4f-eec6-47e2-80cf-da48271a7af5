<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3 (Python 3.10.11 on win32)" generated="2025-06-02T16:21:16.618621" rpa="false" schemaversion="5">
<suite id="s1" name="Get" source="C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get.robot">
<test id="s1-t1" name="Send All Commands" line="130">
<kw name="Setup Environment">
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:21:16.886231" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-02T16:21:16.890228" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:21:17.015749" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:21:16.885232" elapsed="0.130517"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:18.024558" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:17.016773" elapsed="1.007785"/>
</kw>
<arg>houston_server.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:21:16.884231" elapsed="1.141386"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:21:18.029654" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-02T16:21:18.046595" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:21:18.176355" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:21:18.027605" elapsed="0.148750"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:19.182122" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:18.176355" elapsed="1.005767"/>
</kw>
<arg>houston_app.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:21:18.026606" elapsed="1.155516"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:21.186882" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:19.182122" elapsed="2.005796"/>
</kw>
<kw name="Start Process" owner="Process">
<msg time="2025-06-02T16:21:21.189915" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-server_v142\SX-Houston-server_v142\houston_server.exe</msg>
<msg time="2025-06-02T16:21:21.209964" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<arg>${HS_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HS_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-02T16:21:21.187918" elapsed="0.022046"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:26.220430" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:21.211035" elapsed="5.009395"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:26.221459" level="INFO">✅ Houston Server started</msg>
<arg>✅ Houston Server started</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:26.221459" elapsed="0.001038"/>
</kw>
<kw name="Start Process" owner="Process">
<msg time="2025-06-02T16:21:26.224482" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-app_v212\houston_app.exe</msg>
<msg time="2025-06-02T16:21:26.239451" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hcca_handle}</var>
<arg>${HCCA_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HCCA_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-02T16:21:26.222497" elapsed="0.017956"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:31.253168" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:26.240453" elapsed="5.013713"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:31.255173" level="INFO">✅ HCCA started</msg>
<arg>✅ HCCA started</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:31.254166" elapsed="0.002007"/>
</kw>
<return>
<value>${hs_handle}</value>
<value>${hcca_handle}</value>
<status status="PASS" start="2025-06-02T16:21:31.257158" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:31.259167" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<msg time="2025-06-02T16:21:31.259167" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<var>${hcca_handle}</var>
<doc>Starts both Houston Server and HCCA</doc>
<status status="PASS" start="2025-06-02T16:21:16.884231" elapsed="14.374936"/>
</kw>
<kw name="Create List" owner="BuiltIn">
<msg time="2025-06-02T16:21:31.262230" level="INFO">@{commands} = [ get_app_period | set_time | reset_config | get_version ]</msg>
<var>@{commands}</var>
<arg>get_app_period</arg>
<arg>set_time</arg>
<arg>reset_config</arg>
<arg>get_version</arg>
<doc>Returns a list containing given items.</doc>
<status status="PASS" start="2025-06-02T16:21:31.261227" elapsed="0.001003"/>
</kw>
<for flavor="IN">
<iter>
<kw name="Send Command">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:31.267227" level="INFO">Attempting to send command: get_app_period</msg>
<arg>Attempting to send command: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:31.266229" elapsed="0.002928"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:31.275194" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:31.274172" elapsed="0.001022"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="PASS" start="2025-06-02T16:21:31.276152" elapsed="0.098779"/>
</kw>
<msg time="2025-06-02T16:21:31.374931" level="INFO">${location} = True</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:31.276152" elapsed="0.098779"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:31.375932" elapsed="0.000000"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:31.374931" elapsed="0.001001"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:31.375932" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:31.375932" level="INFO">${search_box_found} = True</msg>
<var>${search_box_found}</var>
<arg>${SEARCH_BOX_IMAGE}</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:31.272161" elapsed="0.103771"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Typewrite" owner="pyautogui">
<arg>${command_name}</arg>
<doc>Performs a keyboard key press down, followed by a release, for each of
the characters in message.</doc>
<status status="PASS" start="2025-06-02T16:21:31.376931" elapsed="0.113642"/>
</kw>
<arg>${search_box_found}</arg>
<arg>pyautogui.typewrite</arg>
<arg>${command_name}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>⚠️ Search box not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:31.375932" elapsed="0.114641"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:32.492637" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:31.491408" elapsed="1.002004"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:21:32.495463" level="INFO">${command_image} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png</msg>
<var>${command_image}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${command_name}.png</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:21:32.493412" elapsed="0.002051"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:32.499247" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:32.498415" elapsed="0.001830"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<msg time="2025-06-02T16:21:32.598947" level="FAIL">ImageNotFoundException</msg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="FAIL" start="2025-06-02T16:21:32.501236" elapsed="0.099276">ImageNotFoundException</status>
</kw>
<msg time="2025-06-02T16:21:32.600512" level="INFO">${location} = False</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:32.501236" elapsed="0.099276"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click" owner="pyautogui">
<arg>${location}</arg>
<doc>Performs pressing a mouse button down and then immediately releasing it. Returns ``None``.</doc>
<status status="PASS" start="2025-06-02T16:21:32.600512" elapsed="0.112750"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:32.600512" elapsed="0.112750"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:32.713796" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:32.713796" level="INFO">${command_found} = False</msg>
<var>${command_found}</var>
<arg>${command_image}</arg>
<arg>double_click=True</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:32.496409" elapsed="0.217387"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:32.716795" level="INFO">⚠️ Command not found: get_app_period</msg>
<arg>⚠️ Command not found: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:32.715793" elapsed="0.001002"/>
</kw>
<arg>not ${command_found}</arg>
<arg>Log</arg>
<arg>⚠️ Command not found: ${command_name}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:32.714791" elapsed="0.002004"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:33.719037" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:32.717795" elapsed="1.001242"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:33.722106" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:33.721060" elapsed="0.001046"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="PASS" start="2025-06-02T16:21:33.723054" elapsed="0.090660"/>
</kw>
<msg time="2025-06-02T16:21:33.813714" level="INFO">${location} = True</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:33.723054" elapsed="0.090660"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:33.813714" elapsed="0.000000"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:33.813714" elapsed="0.000000"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:33.814713" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:33.814713" level="INFO">${send_button_found} = True</msg>
<var>${send_button_found}</var>
<arg>${SEND_BUTTON_IMAGE}</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:33.720052" elapsed="0.094661"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>not ${send_button_found}</arg>
<arg>Log</arg>
<arg>⚠️ Send button not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:33.814713" elapsed="0.000976"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-02T16:21:33.815689" level="INFO">${success} = False</msg>
<var>${success}</var>
<arg>${search_box_found} and ${command_found} and ${send_button_found}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-02T16:21:33.815689" elapsed="0.000000"/>
</kw>
<return>
<value>${success}</value>
<status status="PASS" start="2025-06-02T16:21:33.815689" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:33.815689" level="INFO">${cmd_success} = False</msg>
<var>${cmd_success}</var>
<arg>${cmd}</arg>
<doc>Sends a command using PyAutoGUI</doc>
<status status="PASS" start="2025-06-02T16:21:31.265228" elapsed="2.550461"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:33.817713" level="INFO">⚠️ Failed to send command: get_app_period</msg>
<arg>⚠️ Failed to send command: ${cmd}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:33.816713" elapsed="0.001000"/>
</kw>
<arg>not ${cmd_success}</arg>
<arg>Log</arg>
<arg>⚠️ Failed to send command: ${cmd}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>✅ Successfully sent command: ${cmd}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:33.815689" elapsed="0.002024"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:35.820458" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:33.817713" elapsed="2.002745"/>
</kw>
<var name="${cmd}">get_app_period</var>
<status status="PASS" start="2025-06-02T16:21:31.264233" elapsed="4.557009"/>
</iter>
<iter>
<kw name="Send Command">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:35.823296" level="INFO">Attempting to send command: set_time</msg>
<arg>Attempting to send command: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:35.822247" elapsed="0.001997"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:35.826294" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:35.825245" elapsed="0.001997"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="PASS" start="2025-06-02T16:21:35.827242" elapsed="0.085949"/>
</kw>
<msg time="2025-06-02T16:21:35.913191" level="INFO">${location} = True</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:35.827242" elapsed="0.085949"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:35.913191" elapsed="0.000000"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:35.913191" elapsed="0.001002"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:35.914193" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:35.914193" level="INFO">${search_box_found} = True</msg>
<var>${search_box_found}</var>
<arg>${SEARCH_BOX_IMAGE}</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:35.824244" elapsed="0.089949"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Typewrite" owner="pyautogui">
<arg>${command_name}</arg>
<doc>Performs a keyboard key press down, followed by a release, for each of
the characters in message.</doc>
<status status="PASS" start="2025-06-02T16:21:35.914193" elapsed="0.108656"/>
</kw>
<arg>${search_box_found}</arg>
<arg>pyautogui.typewrite</arg>
<arg>${command_name}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>⚠️ Search box not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:35.914193" elapsed="0.108656"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:37.035161" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:36.023686" elapsed="1.011475"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:21:37.036127" level="INFO">${command_image} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png</msg>
<var>${command_image}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${command_name}.png</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:21:37.035161" elapsed="0.000966"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:37.037161" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:37.037161" elapsed="0.000999"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<msg time="2025-06-02T16:21:37.071510" level="FAIL">OSError: Failed to read C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png because file is missing, has improper permissions, or is an unsupported or invalid format</msg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="FAIL" start="2025-06-02T16:21:37.038160" elapsed="0.034353">OSError: Failed to read C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png because file is missing, has improper permissions, or is an unsupported or invalid format</status>
</kw>
<msg time="2025-06-02T16:21:37.072513" level="INFO">${location} = False</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:37.038160" elapsed="0.034353"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click" owner="pyautogui">
<arg>${location}</arg>
<doc>Performs pressing a mouse button down and then immediately releasing it. Returns ``None``.</doc>
<status status="PASS" start="2025-06-02T16:21:37.073549" elapsed="0.118366"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:37.072513" elapsed="0.119402"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:37.191915" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:37.192934" level="INFO">${command_found} = False</msg>
<var>${command_found}</var>
<arg>${command_image}</arg>
<arg>double_click=True</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:37.036127" elapsed="0.156807"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:37.193933" level="INFO">⚠️ Command not found: set_time</msg>
<arg>⚠️ Command not found: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:37.193933" elapsed="0.001007"/>
</kw>
<arg>not ${command_found}</arg>
<arg>Log</arg>
<arg>⚠️ Command not found: ${command_name}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:37.192934" elapsed="0.002006"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:38.208135" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:37.194940" elapsed="1.013195"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:38.210141" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:38.210141" elapsed="0.000971"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="PASS" start="2025-06-02T16:21:38.212119" elapsed="0.102359"/>
</kw>
<msg time="2025-06-02T16:21:38.314478" level="INFO">${location} = True</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:38.211112" elapsed="0.103366"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:38.314478" elapsed="0.000984"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:38.314478" elapsed="0.000984"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:38.315462" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:38.315462" level="INFO">${send_button_found} = True</msg>
<var>${send_button_found}</var>
<arg>${SEND_BUTTON_IMAGE}</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:38.209116" elapsed="0.106346"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>not ${send_button_found}</arg>
<arg>Log</arg>
<arg>⚠️ Send button not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:38.315462" elapsed="0.000990"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-02T16:21:38.317458" level="INFO">${success} = False</msg>
<var>${success}</var>
<arg>${search_box_found} and ${command_found} and ${send_button_found}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-02T16:21:38.316452" elapsed="0.001006"/>
</kw>
<return>
<value>${success}</value>
<status status="PASS" start="2025-06-02T16:21:38.317458" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:38.317458" level="INFO">${cmd_success} = False</msg>
<var>${cmd_success}</var>
<arg>${cmd}</arg>
<doc>Sends a command using PyAutoGUI</doc>
<status status="PASS" start="2025-06-02T16:21:35.822247" elapsed="2.495211"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:38.318451" level="INFO">⚠️ Failed to send command: set_time</msg>
<arg>⚠️ Failed to send command: ${cmd}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:38.318451" elapsed="0.000000"/>
</kw>
<arg>not ${cmd_success}</arg>
<arg>Log</arg>
<arg>⚠️ Failed to send command: ${cmd}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>✅ Successfully sent command: ${cmd}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:38.317458" elapsed="0.000993"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:40.323539" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:38.318451" elapsed="2.005088"/>
</kw>
<var name="${cmd}">set_time</var>
<status status="PASS" start="2025-06-02T16:21:35.821242" elapsed="4.502297"/>
</iter>
<iter>
<kw name="Send Command">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:40.325539" level="INFO">Attempting to send command: reset_config</msg>
<arg>Attempting to send command: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:40.325539" elapsed="0.006001"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:40.332539" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:40.332539" elapsed="0.000997"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="PASS" start="2025-06-02T16:21:40.333536" elapsed="0.082000"/>
</kw>
<msg time="2025-06-02T16:21:40.416536" level="INFO">${location} = True</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:40.333536" elapsed="0.083000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:40.416536" elapsed="0.000000"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:40.416536" elapsed="0.000000"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:40.416536" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:40.417536" level="INFO">${search_box_found} = True</msg>
<var>${search_box_found}</var>
<arg>${SEARCH_BOX_IMAGE}</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:40.331540" elapsed="0.085996"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Typewrite" owner="pyautogui">
<arg>${command_name}</arg>
<doc>Performs a keyboard key press down, followed by a release, for each of
the characters in message.</doc>
<status status="PASS" start="2025-06-02T16:21:40.417536" elapsed="0.114627"/>
</kw>
<arg>${search_box_found}</arg>
<arg>pyautogui.typewrite</arg>
<arg>${command_name}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>⚠️ Search box not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:40.417536" elapsed="0.114627"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:41.548371" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:40.532855" elapsed="1.015516"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:21:41.549051" level="INFO">${command_image} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png</msg>
<var>${command_image}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${command_name}.png</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:21:41.549051" elapsed="0.000000"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:41.550087" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:41.550087" elapsed="0.000971"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<msg time="2025-06-02T16:21:41.585977" level="FAIL">OSError: Failed to read C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png because file is missing, has improper permissions, or is an unsupported or invalid format</msg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="FAIL" start="2025-06-02T16:21:41.551058" elapsed="0.035930">OSError: Failed to read C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png because file is missing, has improper permissions, or is an unsupported or invalid format</status>
</kw>
<msg time="2025-06-02T16:21:41.586988" level="INFO">${location} = False</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:41.551058" elapsed="0.035930"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click" owner="pyautogui">
<arg>${location}</arg>
<doc>Performs pressing a mouse button down and then immediately releasing it. Returns ``None``.</doc>
<status status="PASS" start="2025-06-02T16:21:41.586988" elapsed="0.115433"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:41.586988" elapsed="0.115433"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:41.702421" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:41.703386" level="INFO">${command_found} = False</msg>
<var>${command_found}</var>
<arg>${command_image}</arg>
<arg>double_click=True</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:41.550087" elapsed="0.153299"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:41.703386" level="INFO">⚠️ Command not found: reset_config</msg>
<arg>⚠️ Command not found: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:41.703386" elapsed="0.001020"/>
</kw>
<arg>not ${command_found}</arg>
<arg>Log</arg>
<arg>⚠️ Command not found: ${command_name}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:41.703386" elapsed="0.001020"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:42.710912" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:41.704406" elapsed="1.006506"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:42.711944" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:42.711944" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="PASS" start="2025-06-02T16:21:42.713965" elapsed="0.072702"/>
</kw>
<msg time="2025-06-02T16:21:42.786667" level="INFO">${location} = True</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:42.712943" elapsed="0.073724"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:42.787666" elapsed="0.000000"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:42.786667" elapsed="0.000999"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:42.787666" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:42.787666" level="INFO">${send_button_found} = True</msg>
<var>${send_button_found}</var>
<arg>${SEND_BUTTON_IMAGE}</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:42.710912" elapsed="0.076754"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>not ${send_button_found}</arg>
<arg>Log</arg>
<arg>⚠️ Send button not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:42.787666" elapsed="0.000968"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-02T16:21:42.788634" level="INFO">${success} = False</msg>
<var>${success}</var>
<arg>${search_box_found} and ${command_found} and ${send_button_found}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-02T16:21:42.788634" elapsed="0.000000"/>
</kw>
<return>
<value>${success}</value>
<status status="PASS" start="2025-06-02T16:21:42.788634" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:42.788634" level="INFO">${cmd_success} = False</msg>
<var>${cmd_success}</var>
<arg>${cmd}</arg>
<doc>Sends a command using PyAutoGUI</doc>
<status status="PASS" start="2025-06-02T16:21:40.324538" elapsed="2.464096"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:42.789631" level="INFO">⚠️ Failed to send command: reset_config</msg>
<arg>⚠️ Failed to send command: ${cmd}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:42.789631" elapsed="0.000591"/>
</kw>
<arg>not ${cmd_success}</arg>
<arg>Log</arg>
<arg>⚠️ Failed to send command: ${cmd}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>✅ Successfully sent command: ${cmd}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:42.788634" elapsed="0.001588"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:44.803809" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:42.790222" elapsed="2.013587"/>
</kw>
<var name="${cmd}">reset_config</var>
<status status="PASS" start="2025-06-02T16:21:40.323539" elapsed="4.480270"/>
</iter>
<iter>
<kw name="Send Command">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:44.807880" level="INFO">Attempting to send command: get_version</msg>
<arg>Attempting to send command: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:44.806842" elapsed="0.001992"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:44.812883" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:44.811885" elapsed="0.000998"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="PASS" start="2025-06-02T16:21:44.814860" elapsed="0.124007"/>
</kw>
<msg time="2025-06-02T16:21:44.938867" level="INFO">${location} = True</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:44.813838" elapsed="0.125029"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:44.939867" elapsed="0.000000"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:44.939867" elapsed="0.000000"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:44.939867" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:44.939867" level="INFO">${search_box_found} = True</msg>
<var>${search_box_found}</var>
<arg>${SEARCH_BOX_IMAGE}</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:44.809832" elapsed="0.130035"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Typewrite" owner="pyautogui">
<arg>${command_name}</arg>
<doc>Performs a keyboard key press down, followed by a release, for each of
the characters in message.</doc>
<status status="PASS" start="2025-06-02T16:21:44.940867" elapsed="0.114376"/>
</kw>
<arg>${search_box_found}</arg>
<arg>pyautogui.typewrite</arg>
<arg>${command_name}</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>⚠️ Search box not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:44.939867" elapsed="0.115376"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:46.067796" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:45.055243" elapsed="1.013322"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:21:46.070575" level="INFO">${command_image} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png</msg>
<var>${command_image}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${command_name}.png</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:21:46.068565" elapsed="0.002010"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:46.074574" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:46.073572" elapsed="0.001997"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<msg time="2025-06-02T16:21:46.147995" level="FAIL">OSError: Failed to read C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png because file is missing, has improper permissions, or is an unsupported or invalid format</msg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="FAIL" start="2025-06-02T16:21:46.077578" elapsed="0.071409">OSError: Failed to read C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png because file is missing, has improper permissions, or is an unsupported or invalid format</status>
</kw>
<msg time="2025-06-02T16:21:46.148987" level="INFO">${location} = False</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:46.076562" elapsed="0.072425"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click" owner="pyautogui">
<arg>${location}</arg>
<doc>Performs pressing a mouse button down and then immediately releasing it. Returns ``None``.</doc>
<status status="PASS" start="2025-06-02T16:21:46.149989" elapsed="0.123591"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:46.149989" elapsed="0.123591"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:46.273580" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:46.274583" level="INFO">${command_found} = False</msg>
<var>${command_found}</var>
<arg>${command_image}</arg>
<arg>double_click=True</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:46.071575" elapsed="0.203008"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:46.276582" level="INFO">⚠️ Command not found: get_version</msg>
<arg>⚠️ Command not found: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:46.275585" elapsed="0.000997"/>
</kw>
<arg>not ${command_found}</arg>
<arg>Log</arg>
<arg>⚠️ Command not found: ${command_name}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:46.274583" elapsed="0.003001"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:47.286750" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:46.277584" elapsed="1.009166"/>
</kw>
<kw name="Find And Click Image">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:47.287753" level="INFO">Looking for image: C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png</msg>
<arg>Looking for image: ${image_path}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:47.287753" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<msg time="2025-06-02T16:21:47.375379" level="FAIL">ImageNotFoundException</msg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="FAIL" start="2025-06-02T16:21:47.288753" elapsed="0.087626">ImageNotFoundException</status>
</kw>
<msg time="2025-06-02T16:21:47.376379" level="INFO">${location} = False</msg>
<var>${location}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${image_path}</arg>
<arg>confidence=${confidence}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:21:47.287753" elapsed="0.088626"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Click" owner="pyautogui">
<arg>${location}</arg>
<doc>Performs pressing a mouse button down and then immediately releasing it. Returns ``None``.</doc>
<status status="PASS" start="2025-06-02T16:21:47.376379" elapsed="0.130270"/>
</kw>
<arg>${location}</arg>
<arg>Run Keyword If</arg>
<arg>${double_click}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>${location}</arg>
<arg>ELSE</arg>
<arg>pyautogui.click</arg>
<arg>${location}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:47.376379" elapsed="0.130270"/>
</kw>
<return>
<value>${location}</value>
<status status="PASS" start="2025-06-02T16:21:47.507649" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:47.507649" level="INFO">${send_button_found} = False</msg>
<var>${send_button_found}</var>
<arg>${SEND_BUTTON_IMAGE}</arg>
<doc>Finds and clicks an image on screen</doc>
<status status="PASS" start="2025-06-02T16:21:47.286750" elapsed="0.220899"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:47.509651" level="INFO">⚠️ Send button not found</msg>
<arg>⚠️ Send button not found</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:47.508652" elapsed="0.002016"/>
</kw>
<arg>not ${send_button_found}</arg>
<arg>Log</arg>
<arg>⚠️ Send button not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:47.508652" elapsed="0.002016"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-06-02T16:21:47.511649" level="INFO">${success} = False</msg>
<var>${success}</var>
<arg>${search_box_found} and ${command_found} and ${send_button_found}</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-06-02T16:21:47.510668" elapsed="0.000981"/>
</kw>
<return>
<value>${success}</value>
<status status="PASS" start="2025-06-02T16:21:47.511649" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:21:47.511649" level="INFO">${cmd_success} = False</msg>
<var>${cmd_success}</var>
<arg>${cmd}</arg>
<doc>Sends a command using PyAutoGUI</doc>
<status status="PASS" start="2025-06-02T16:21:44.805839" elapsed="2.705810"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:47.512647" level="INFO">⚠️ Failed to send command: get_version</msg>
<arg>⚠️ Failed to send command: ${cmd}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:47.512647" elapsed="0.001010"/>
</kw>
<arg>not ${cmd_success}</arg>
<arg>Log</arg>
<arg>⚠️ Failed to send command: ${cmd}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>✅ Successfully sent command: ${cmd}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:21:47.511649" elapsed="0.002008"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:49.518356" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:47.513657" elapsed="2.004699"/>
</kw>
<var name="${cmd}">get_version</var>
<status status="PASS" start="2025-06-02T16:21:44.804890" elapsed="4.713466"/>
</iter>
<var>${cmd}</var>
<value>@{commands}</value>
<status status="PASS" start="2025-06-02T16:21:31.263227" elapsed="18.255129"/>
</for>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:49.519318" level="INFO">Cleaning up - closing applications...</msg>
<arg>Cleaning up - closing applications...</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:49.518356" elapsed="0.000962"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:21:49.520328" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-02T16:21:49.526319" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:21:49.707031" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:21:49.519318" elapsed="0.187713"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:50.721842" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:49.708032" elapsed="1.013810"/>
</kw>
<arg>houston_server.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:21:49.519318" elapsed="1.202524"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:21:50.722843" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-02T16:21:50.728840" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:21:50.852626" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:21:50.722843" elapsed="0.129783"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:51.858533" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:50.853622" elapsed="1.004911"/>
</kw>
<arg>houston_app.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:21:50.722843" elapsed="1.136738"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:21:51.861220" level="INFO">✅ Test completed</msg>
<arg>✅ Test completed</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:21:51.861220" elapsed="0.001039"/>
</kw>
<doc>Sends all commands in the list</doc>
<status status="PASS" start="2025-06-02T16:21:16.882229" elapsed="34.980998"/>
</test>
<test id="s1-t2" name="Search For Get App Period" line="156">
<kw name="Setup Environment">
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:21:51.873221" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-02T16:21:51.889218" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:21:52.005998" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:21:51.872220" elapsed="0.133778"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:53.023025" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:52.006998" elapsed="1.016027"/>
</kw>
<arg>houston_server.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:21:51.871222" elapsed="1.152800"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:21:53.027056" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-02T16:21:53.048322" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:21:53.217147" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:21:53.026056" elapsed="0.191091"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:54.221334" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:53.218148" elapsed="1.003186"/>
</kw>
<arg>houston_app.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:21:53.025022" elapsed="1.196312"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:21:56.228060" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:54.221334" elapsed="2.006726"/>
</kw>
<kw name="Start Process" owner="Process">
<msg time="2025-06-02T16:21:56.228060" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-server_v142\SX-Houston-server_v142\houston_server.exe</msg>
<msg time="2025-06-02T16:21:56.234066" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<arg>${HS_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HS_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-02T16:21:56.228060" elapsed="0.007005"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:22:01.248227" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:21:56.235065" elapsed="5.013162"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:22:01.249018" level="INFO">✅ Houston Server started</msg>
<arg>✅ Houston Server started</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:22:01.249018" elapsed="0.000920"/>
</kw>
<kw name="Start Process" owner="Process">
<msg time="2025-06-02T16:22:01.251010" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-app_v212\houston_app.exe</msg>
<msg time="2025-06-02T16:22:01.260481" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hcca_handle}</var>
<arg>${HCCA_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HCCA_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-02T16:22:01.249938" elapsed="0.010543"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:22:06.262448" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:22:01.261482" elapsed="5.000966"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:22:06.264387" level="INFO">✅ HCCA started</msg>
<arg>✅ HCCA started</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:22:06.263389" elapsed="0.002006"/>
</kw>
<return>
<value>${hs_handle}</value>
<value>${hcca_handle}</value>
<status status="PASS" start="2025-06-02T16:22:06.265395" elapsed="0.000992"/>
</return>
<msg time="2025-06-02T16:22:06.267437" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<msg time="2025-06-02T16:22:06.267437" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<var>${hcca_handle}</var>
<doc>Starts both Houston Server and HCCA</doc>
<status status="PASS" start="2025-06-02T16:21:51.869226" elapsed="14.398211"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:22:06.269561" level="INFO">Please take a screenshot of the empty search field and save it as "search_field.png"</msg>
<arg>Please take a screenshot of the empty search field and save it as "search_field.png"</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:22:06.268517" elapsed="0.002240"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:22:06.272817" level="INFO">Press Enter when done...</msg>
<arg>Press Enter when done...</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:22:06.271809" elapsed="0.002013"/>
</kw>
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:22:06.276799" level="INFO">Starting process:
cmd /c pause</msg>
<msg time="2025-06-02T16:22:06.297807" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:22:06.352201" level="INFO">Process completed.</msg>
<arg>cmd</arg>
<arg>/c</arg>
<arg>pause</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:22:06.275801" elapsed="0.076400"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:22:06.353198" level="INFO">Please search for "get_app_period", then take a screenshot of it in the results and save as "get_app_period.png"</msg>
<arg>Please search for "get_app_period", then take a screenshot of it in the results and save as "get_app_period.png"</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:22:06.353198" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:22:06.354198" level="INFO">Press Enter when done...</msg>
<arg>Press Enter when done...</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:22:06.354198" elapsed="0.000000"/>
</kw>
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:22:06.355200" level="INFO">Starting process:
cmd /c pause</msg>
<msg time="2025-06-02T16:22:06.360240" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:22:06.395774" level="INFO">Process completed.</msg>
<arg>cmd</arg>
<arg>/c</arg>
<arg>pause</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:22:06.354198" elapsed="0.041576"/>
</kw>
<kw name="Search And Double Click Command">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:22:06.396743" level="INFO">Searching for command: get_app_period</msg>
<arg>Searching for command: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:22:06.396743" elapsed="0.000000"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-02T16:22:06.397743" level="INFO">${search_field_image} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_field.png</msg>
<var>${search_field_image}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\search_field.png</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-02T16:22:06.396743" elapsed="0.001000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<msg time="2025-06-02T16:22:06.428326" level="FAIL">OSError: Failed to read C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_field.png because file is missing, has improper permissions, or is an unsupported or invalid format</msg>
<arg>${search_field_image}</arg>
<arg>confidence=${CONFIDENCE_LEVEL}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="FAIL" start="2025-06-02T16:22:06.397743" elapsed="0.031594">OSError: Failed to read C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_field.png because file is missing, has improper permissions, or is an unsupported or invalid format</status>
</kw>
<msg time="2025-06-02T16:22:06.429337" level="INFO">${search_field_found} = False</msg>
<var>${search_field_found}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>${search_field_image}</arg>
<arg>confidence=${CONFIDENCE_LEVEL}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:22:06.397743" elapsed="0.031594"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:22:06.430336" level="INFO">⚠️ Search field not found</msg>
<arg>⚠️ Search field not found</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:22:06.430336" elapsed="0.000000"/>
</kw>
<arg>${search_field_found}</arg>
<arg>pyautogui.click</arg>
<arg>pyautogui.locateOnScreen(${search_field_image}, confidence=${CONFIDENCE_LEVEL})</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>⚠️ Search field not found</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:22:06.429337" elapsed="0.000999"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<arg>${search_field_found}</arg>
<arg>pyautogui.typewrite</arg>
<arg>${command_name}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:22:06.431337" elapsed="0.000000"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:22:07.446350" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:22:06.431337" elapsed="1.015013"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="Locate On Screen" owner="pyautogui">
<msg time="2025-06-02T16:22:07.520710" level="FAIL">ImageNotFoundException</msg>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${command_name}.png</arg>
<arg>confidence=${CONFIDENCE_LEVEL}</arg>
<doc>TODO - rewrite this
minSearchTime - amount of time in seconds to repeat taking
screenshots and trying to locate a match.  The default of 0 performs
a single search.</doc>
<status status="FAIL" start="2025-06-02T16:22:07.447379" elapsed="0.074333">ImageNotFoundException</status>
</kw>
<msg time="2025-06-02T16:22:07.521712" level="INFO">${command_found} = False</msg>
<var>${command_found}</var>
<arg>pyautogui.locateOnScreen</arg>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${command_name}.png</arg>
<arg>confidence=${CONFIDENCE_LEVEL}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-02T16:22:07.446350" elapsed="0.075362"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:22:07.521712" level="INFO">⚠️ Command not found in results: get_app_period</msg>
<arg>⚠️ Command not found in results: ${command_name}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:22:07.521712" elapsed="0.001033"/>
</kw>
<arg>${command_found}</arg>
<arg>pyautogui.doubleClick</arg>
<arg>pyautogui.locateOnScreen(C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${command_name}.png, confidence=${CONFIDENCE_LEVEL})</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>⚠️ Command not found in results: ${command_name}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:22:07.521712" elapsed="0.001033"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:22:08.523590" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:22:07.522745" elapsed="1.000845"/>
</kw>
<return>
<value>${command_found}</value>
<status status="PASS" start="2025-06-02T16:22:08.524402" elapsed="0.000000"/>
</return>
<msg time="2025-06-02T16:22:08.524402" level="INFO">${success} = False</msg>
<var>${success}</var>
<arg>get_app_period</arg>
<doc>Searches for a command in the search field and double-clicks it</doc>
<status status="PASS" start="2025-06-02T16:22:06.395774" elapsed="2.128628"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:22:08.526406" level="INFO">❌ Failed to find and double-click get_app_period</msg>
<arg>❌ Failed to find and double-click get_app_period</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:22:08.526406" elapsed="0.001052"/>
</kw>
<arg>${success}</arg>
<arg>Log</arg>
<arg>✅ Successfully found and double-clicked get_app_period</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>❌ Failed to find and double-click get_app_period</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-02T16:22:08.525408" elapsed="0.002050"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:22:13.544013" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:22:08.528700" elapsed="5.015313"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:22:13.544686" level="INFO">Cleaning up - closing applications...</msg>
<arg>Cleaning up - closing applications...</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:22:13.544686" elapsed="0.001001"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:22:13.546686" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-02T16:22:13.550687" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:22:13.670119" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:22:13.546686" elapsed="0.124448"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:22:14.682700" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:22:13.671134" elapsed="1.012566"/>
</kw>
<arg>houston_server.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:22:13.545687" elapsed="1.139010"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-02T16:22:14.688700" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-02T16:22:14.700700" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-02T16:22:14.840760" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-02T16:22:14.687693" elapsed="0.153067"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-02T16:22:15.853338" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-02T16:22:14.840760" elapsed="1.012578"/>
</kw>
<arg>houston_app.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-02T16:22:14.685701" elapsed="1.167637"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-02T16:22:15.854167" level="INFO">✅ Test completed</msg>
<arg>✅ Test completed</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-02T16:22:15.854167" elapsed="0.001000"/>
</kw>
<doc>Searches for get_app_period command and double-clicks it</doc>
<status status="PASS" start="2025-06-02T16:21:51.866215" elapsed="23.988952"/>
</test>
<status status="PASS" start="2025-06-02T16:21:16.620588" elapsed="59.235578"/>
</suite>
<statistics>
<total>
<stat pass="2" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Get" id="s1" pass="2" fail="0" skip="0">Get</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
