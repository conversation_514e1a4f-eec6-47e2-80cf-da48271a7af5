<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3 (Python 3.10.11 on win32)" generated="2025-06-03T09:46:53.718135" rpa="false" schemaversion="5">
<suite id="s1" name="Simple Test" source="C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\simple_test.robot">
<test id="s1-t1" name="Basic Environment Test" line="69">
<kw name="Check Required Files">
<kw name="Create List" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.773805" level="INFO">@{required_files} = [ search_box.png | send_button.png | get_app_period.png | search_field.png | set_time.png | reset_config.png | get_version.png ]</msg>
<var>@{required_files}</var>
<arg>search_box.png</arg>
<arg>send_button.png</arg>
<arg>get_app_period.png</arg>
<arg>search_field.png</arg>
<arg>set_time.png</arg>
<arg>reset_config.png</arg>
<arg>get_version.png</arg>
<doc>Returns a list containing given items.</doc>
<status status="PASS" start="2025-06-03T09:46:53.773805" elapsed="0.000000"/>
</kw>
<kw name="Create List" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.774807" level="INFO">${missing_files} = []</msg>
<var>${missing_files}</var>
<doc>Returns a list containing given items.</doc>
<status status="PASS" start="2025-06-03T09:46:53.773805" elapsed="0.001130"/>
</kw>
<for flavor="IN">
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.774935" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:46:53.774935" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:46:53.775804" level="FAIL">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png' does not exist.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="FAIL" start="2025-06-03T09:46:53.775804" elapsed="0.000000">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_box.png' does not exist.</status>
</kw>
<msg time="2025-06-03T09:46:53.775804" level="INFO">${exists} = False</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:46:53.775804" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.776767" level="INFO">❌ Missing: search_box.png</msg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:46:53.776767" elapsed="0.000998"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-03T09:46:53.778080" elapsed="0.000000"/>
</kw>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-06-03T09:46:53.776767" elapsed="0.001313"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:46:53.776767" elapsed="0.001313"/>
</kw>
<var name="${file}">search_box.png</var>
<status status="PASS" start="2025-06-03T09:46:53.774935" elapsed="0.003830"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.778765" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:46:53.778765" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:46:53.779802" level="INFO" html="true">File '&lt;a href="file://C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png"&gt;C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\send_button.png&lt;/a&gt;' exists.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="PASS" start="2025-06-03T09:46:53.779802" elapsed="0.000000"/>
</kw>
<msg time="2025-06-03T09:46:53.780683" level="INFO">${exists} = True</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:46:53.779802" elapsed="0.000961"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.780763" level="INFO">✅ Found: send_button.png</msg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:46:53.780763" elapsed="0.001038"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:46:53.780763" elapsed="0.001038"/>
</kw>
<var name="${file}">send_button.png</var>
<status status="PASS" start="2025-06-03T09:46:53.778765" elapsed="0.003036"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.782765" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:46:53.781801" elapsed="0.000964"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:46:53.783763" level="FAIL">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png' does not exist.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="FAIL" start="2025-06-03T09:46:53.782765" elapsed="0.000998">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_app_period.png' does not exist.</status>
</kw>
<msg time="2025-06-03T09:46:53.783763" level="INFO">${exists} = False</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:46:53.782765" elapsed="0.000998"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.784763" level="INFO">❌ Missing: get_app_period.png</msg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:46:53.784763" elapsed="0.000000"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-03T09:46:53.784763" elapsed="0.001001"/>
</kw>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-06-03T09:46:53.783763" elapsed="0.002001"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:46:53.783763" elapsed="0.002001"/>
</kw>
<var name="${file}">get_app_period.png</var>
<status status="PASS" start="2025-06-03T09:46:53.781801" elapsed="0.003963"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.786761" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_field.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:46:53.786761" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:46:53.787799" level="INFO" html="true">File '&lt;a href="file://C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_field.png"&gt;C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\search_field.png&lt;/a&gt;' exists.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="PASS" start="2025-06-03T09:46:53.787799" elapsed="0.000000"/>
</kw>
<msg time="2025-06-03T09:46:53.787799" level="INFO">${exists} = True</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:46:53.786761" elapsed="0.001038"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.788794" level="INFO">✅ Found: search_field.png</msg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:46:53.788794" elapsed="0.000000"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:46:53.788794" elapsed="0.000968"/>
</kw>
<var name="${file}">search_field.png</var>
<status status="PASS" start="2025-06-03T09:46:53.785764" elapsed="0.003998"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.790760" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:46:53.789762" elapsed="0.000998"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:46:53.790760" level="FAIL">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png' does not exist.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="FAIL" start="2025-06-03T09:46:53.790760" elapsed="0.000996">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\set_time.png' does not exist.</status>
</kw>
<msg time="2025-06-03T09:46:53.791756" level="INFO">${exists} = False</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:46:53.790760" elapsed="0.000996"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.792757" level="INFO">❌ Missing: set_time.png</msg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:46:53.791756" elapsed="0.001330"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-03T09:46:53.793086" elapsed="0.000000"/>
</kw>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-06-03T09:46:53.791756" elapsed="0.002000"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:46:53.791756" elapsed="0.002000"/>
</kw>
<var name="${file}">set_time.png</var>
<status status="PASS" start="2025-06-03T09:46:53.789762" elapsed="0.003994"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.793756" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:46:53.793756" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:46:53.795221" level="FAIL">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png' does not exist.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="FAIL" start="2025-06-03T09:46:53.794755" elapsed="0.000466">File 'C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\reset_config.png' does not exist.</status>
</kw>
<msg time="2025-06-03T09:46:53.795221" level="INFO">${exists} = False</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:46:53.794755" elapsed="0.000466"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Run Keywords" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.795754" level="INFO">❌ Missing: reset_config.png</msg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:46:53.795754" elapsed="0.001003"/>
</kw>
<kw name="Append To List" owner="Collections">
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Adds ``values`` to the end of ``list``.</doc>
<status status="PASS" start="2025-06-03T09:46:53.797081" elapsed="0.000000"/>
</kw>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Executes all the given keywords in a sequence.</doc>
<status status="PASS" start="2025-06-03T09:46:53.795754" elapsed="0.002003"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:46:53.795754" elapsed="0.002003"/>
</kw>
<var name="${file}">reset_config.png</var>
<status status="PASS" start="2025-06-03T09:46:53.793756" elapsed="0.004001"/>
</iter>
<iter>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.797757" level="INFO">${file_path} = C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png</msg>
<var>${file_path}</var>
<arg>C:\\Users\\<USER>\\Downloads\\Test_automation-HS_hcca-AUTOMATION\\Test_automation-HS_hcca-AUTOMATION\\${file}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-06-03T09:46:53.797757" elapsed="0.000000"/>
</kw>
<kw name="Run Keyword And Return Status" owner="BuiltIn">
<kw name="File Should Exist" owner="OperatingSystem">
<msg time="2025-06-03T09:46:53.799852" level="INFO" html="true">File '&lt;a href="file://C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png"&gt;C:\Users\<USER>\Downloads\Test_automation-HS_hcca-AUTOMATION\Test_automation-HS_hcca-AUTOMATION\get_version.png&lt;/a&gt;' exists.</msg>
<arg>${file_path}</arg>
<doc>Fails unless the given ``path`` points to an existing file.</doc>
<status status="PASS" start="2025-06-03T09:46:53.798753" elapsed="0.001099"/>
</kw>
<msg time="2025-06-03T09:46:53.799852" level="INFO">${exists} = True</msg>
<var>${exists}</var>
<arg>OperatingSystem.File Should Exist</arg>
<arg>${file_path}</arg>
<doc>Runs the given keyword with given arguments and returns the status as a Boolean value.</doc>
<status status="PASS" start="2025-06-03T09:46:53.798753" elapsed="0.001099"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.801753" level="INFO">✅ Found: get_version.png</msg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:46:53.800753" elapsed="0.001308"/>
</kw>
<arg>${exists}</arg>
<arg>Log</arg>
<arg>✅ Found: ${file}</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Run Keywords</arg>
<arg>Log</arg>
<arg>❌ Missing: ${file}</arg>
<arg>console=True</arg>
<arg>AND</arg>
<arg>Append To List</arg>
<arg>${missing_files}</arg>
<arg>${file}</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:46:53.800753" elapsed="0.001308"/>
</kw>
<var name="${file}">get_version.png</var>
<status status="PASS" start="2025-06-03T09:46:53.797757" elapsed="0.004304"/>
</iter>
<var>${file}</var>
<value>@{required_files}</value>
<status status="PASS" start="2025-06-03T09:46:53.774935" elapsed="0.027126"/>
</for>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.803073" level="INFO">Length is 4.</msg>
<msg time="2025-06-03T09:46:53.803073" level="INFO">${missing_count} = 4</msg>
<var>${missing_count}</var>
<arg>${missing_files}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-03T09:46:53.802061" elapsed="0.001012"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:46:53.804072" level="INFO">⚠️ 4 image files are missing. Please capture them before running the full test.</msg>
<arg>⚠️ ${missing_count} image files are missing. Please capture them before running the full test.</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:46:53.804072" elapsed="0.000674"/>
</kw>
<arg>${missing_count} &gt; 0</arg>
<arg>Log</arg>
<arg>⚠️ ${missing_count} image files are missing. Please capture them before running the full test.</arg>
<arg>console=True</arg>
<arg>ELSE</arg>
<arg>Log</arg>
<arg>✅ All required image files are present!</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:46:53.803073" elapsed="0.001673"/>
</kw>
<return>
<value>${missing_files}</value>
<status status="PASS" start="2025-06-03T09:46:53.804746" elapsed="0.000000"/>
</return>
<msg time="2025-06-03T09:46:53.804746" level="INFO">${missing_files} = ['search_box.png', 'get_app_period.png', 'set_time.png', 'reset_config.png']</msg>
<var>${missing_files}</var>
<doc>Checks if all required image files exist</doc>
<status status="PASS" start="2025-06-03T09:46:53.772768" elapsed="0.031978"/>
</kw>
<kw name="Setup Environment">
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-03T09:46:53.807639" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-03T09:46:53.818631" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-03T09:46:53.989516" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-03T09:46:53.806639" elapsed="0.183879"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:46:55.002494" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:46:53.990518" elapsed="1.011976"/>
</kw>
<arg>houston_server.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-03T09:46:53.806639" elapsed="1.196423"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-03T09:46:55.004104" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-03T09:46:55.014056" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-03T09:46:55.197930" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-03T09:46:55.004104" elapsed="0.193826"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:46:56.212990" level="INFO">Slept 1 second.</msg>
<arg>1s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:46:55.197930" elapsed="1.015060"/>
</kw>
<arg>houston_app.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="PASS" start="2025-06-03T09:46:55.003062" elapsed="1.209928"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:46:58.227425" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:46:56.213737" elapsed="2.013688"/>
</kw>
<kw name="Start Process" owner="Process">
<msg time="2025-06-03T09:46:58.229430" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-server_v142\SX-Houston-server_v142\houston_server.exe</msg>
<msg time="2025-06-03T09:46:58.251402" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<arg>${HS_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HS_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-03T09:46:58.228427" elapsed="0.022975"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:47:03.262644" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:46:58.251402" elapsed="5.011242"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:47:03.263593" level="INFO">✅ Houston Server started</msg>
<arg>✅ Houston Server started</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:47:03.263593" elapsed="0.001039"/>
</kw>
<kw name="Start Process" owner="Process">
<msg time="2025-06-03T09:47:03.265634" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-app_v212\houston_app.exe</msg>
<msg time="2025-06-03T09:47:03.276669" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hcca_handle}</var>
<arg>${HCCA_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HCCA_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-03T09:47:03.264632" elapsed="0.012037"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:47:08.291763" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:47:03.276669" elapsed="5.015094"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:47:08.292571" level="INFO">✅ HCCA started</msg>
<arg>✅ HCCA started</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:47:08.291763" elapsed="0.001764"/>
</kw>
<return>
<value>${hs_handle}</value>
<value>${hcca_handle}</value>
<status status="PASS" start="2025-06-03T09:47:08.293527" elapsed="0.000000"/>
</return>
<msg time="2025-06-03T09:47:08.294529" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<msg time="2025-06-03T09:47:08.294529" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<var>${hcca_handle}</var>
<doc>Starts both Houston Server and HCCA</doc>
<status status="PASS" start="2025-06-03T09:46:53.805639" elapsed="14.488890"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-03T09:47:18.308508" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-03T09:47:08.294529" elapsed="10.013979"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:47:18.309532" level="INFO">⏳ Applications should now be running. Check them manually.</msg>
<arg>⏳ Applications should now be running. Check them manually.</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:47:18.309532" elapsed="0.000976"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:47:18.311507" level="INFO">📝 This is a good time to capture missing images if needed.</msg>
<arg>📝 This is a good time to capture missing images if needed.</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:47:18.311507" elapsed="0.000999"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-06-03T09:47:18.313507" level="INFO">Length is 4.</msg>
<msg time="2025-06-03T09:47:18.313507" level="INFO">${missing_count} = 4</msg>
<var>${missing_count}</var>
<arg>${missing_files}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-06-03T09:47:18.312506" elapsed="0.001001"/>
</kw>
<kw name="Run Keyword If" owner="BuiltIn">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:47:18.315509" level="INFO">📸 Please capture these missing images: ['search_box.png', 'get_app_period.png', 'set_time.png', 'reset_config.png']</msg>
<arg>📸 Please capture these missing images: ${missing_files}</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:47:18.314505" elapsed="0.001004"/>
</kw>
<arg>${missing_count} &gt; 0</arg>
<arg>Log</arg>
<arg>📸 Please capture these missing images: ${missing_files}</arg>
<arg>console=True</arg>
<doc>Runs the given keyword with the given arguments, if ``condition`` is true.</doc>
<status status="PASS" start="2025-06-03T09:47:18.313507" elapsed="0.002002"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-03T09:47:18.316512" level="INFO">⏰ Keeping applications running for 30 seconds for manual inspection...</msg>
<arg>⏰ Keeping applications running for 30 seconds for manual inspection...</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-03T09:47:18.316512" elapsed="0.000993"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<arg>30s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="FAIL" start="2025-06-03T09:47:18.318504" elapsed="10.979163">Execution terminated by signal</status>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>🧹 Cleaning up - closing applications...</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-03T09:47:29.300707" elapsed="0.000000"/>
</kw>
<kw name="Kill Process If Running">
<arg>houston_server.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="NOT RUN" start="2025-06-03T09:47:29.302696" elapsed="0.000000"/>
</kw>
<kw name="Kill Process If Running">
<arg>houston_app.exe</arg>
<doc>Kills a process if it's running</doc>
<status status="NOT RUN" start="2025-06-03T09:47:29.303664" elapsed="0.000000"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>✅ Basic environment test completed</arg>
<arg>console=True</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-06-03T09:47:29.305694" elapsed="0.000000"/>
</kw>
<doc>Tests basic application startup without UI automation</doc>
<status status="FAIL" start="2025-06-03T09:46:53.771817" elapsed="35.534878">Execution terminated by signal</status>
</test>
<status status="FAIL" start="2025-06-03T09:46:53.719912" elapsed="35.592730"/>
</suite>
<statistics>
<total>
<stat pass="0" fail="1" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Simple Test" id="s1" pass="0" fail="1" skip="0">Simple Test</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
